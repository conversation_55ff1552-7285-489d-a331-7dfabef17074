@startuml
title Combined Workflow: Historical Ingestion, Inspection, and Recovery

left to right direction
skinparam nodesep 10
skinparam ranksep 20
skinparam defaultFontName "Segoe UI"
skinparam defaultFontSize 14

' === Configuration Management ===
package "Configuration Module" #LightYellow {
  [Configuration Service] as configuration_service
}

' === Historical Module ===
package "Historical Module" #LightGreen {
  [Historical Service] as historical_service
}

' === Pattern Module ===
package "Pattern Module" #LightGreen {
  [Candlestick Pattern Service] as candlestick_pattern_service
}

' === Backtest Module ===
package "Backtest Module" #LightGreen {
  [Single Backtest Service] as single_backtest_service
  [Optimizer Backtest Service] as optimizer_backtest_service
}

' === Backtest Module ===
package "Backtest Module" #LightGreen {
  [Mehtod Service] as method_service
  [Method Founder Worker] as method_founder_worker
}

' === Data Platform ===
package "Data Platform" #Moccasin {
  [PostgreSQL Database] as postgresql
  [Redis] as redis
}

' 1. ambil data cron setiap menit
' 2. jika waktu match dengan salah satu data cron, ambil address historical & ambil semua ticker
' 3. secara foreach, 
' 4. hubungi historical untuk ambil data historical sesuai id cron misal 1m (menit) atau 4h (jam) & tickernya
' 5. ambil array parameter di configuration: [1d,1w,1M,3M,6M,1Y]
' 6. foreach didalam foreach & potong data historical sesuai parameter
' 7. ambil candlestick pattern array, kemudian foreach
' 7. hubungi candlestick pattern service untuk ambil pattern yang valid
' 8. hubungi single backtest service untuk single backtest 
' 9. ambil configurations untuk melihat batas probability, drawdown, dan total profit dalam penjumlahan persen. 
' 10. optimizer_backtest_service supaya mendapatkan parameter maksimal, & kemudian backtest lagi
' 11. hubungi method service untuk save method yang hasilnya sesuai dengan configurations (trade_history, trade_summary)
@enduml
