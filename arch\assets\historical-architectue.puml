@startuml
title Combined Workflow: Historical Ingestion, Inspection, and Recovery

left to right direction
skinparam nodesep 10
skinparam ranksep 20
skinparam defaultFontName "Segoe UI"
skinparam defaultFontSize 14

' === External Exchanges ===
package "External Exchanges" #LightBlue {
  [Binance Exchange] as binance
  [Bybit Exchange] as bybit 
}

' === API Integration Layer ===
package "API Integration" #LightYellow {
  [API Adapter] as api_adapter
  [KrakenD Gateway] as krakend
}

' === Historical Module ===
package "Historical Module" #LightGreen {
  [Market Data Ingestor] as ingestor
  [Historical Data Service] as historical_service
  [Data Integrity Checker] as integrity_checker
  [Gap Filter Worker] as gap_filter_worker
}

package "Queue Module" #LightGreen {
  [Queue Service] as queue_service
}

' === Data Platform ===
package "Data Platform" #Moccasin {
  [PostgreSQL Database] as postgresql
}

' === Ingestion Workflow ===
ingestor --> api_adapter : Step 1:\nRequest latest prices
api_adapter --> krakend : Step 2:\nForward to API Gateway
krakend --> binance : Step 3:\nRequest Binance prices
krakend --> bybit : Step 4:\nRequest Bybit prices
binance --> krakend : Step 5:\nReturn Binance data
bybit --> krakend : Step 6:\nReturn Bybit data
krakend --> api_adapter : Step 7:\nReturn raw data
api_adapter --> ingestor : Step 8:\nReturn merged prices
ingestor --> historical_service : Step 9:\nSubmit processed prices
historical_service --> postgresql : Step 10:\nPersist to database
ingestor --> integrity_checker : Step 11:\nTrigger data checker

' === Inspection Workflow ===
integrity_checker --> historical_service : Step 12:\nStart data scan
historical_service --> postgresql : Step 13:\nRead/Write data
integrity_checker --> queue_service : Step 14:\nSignal missing data
queue_service --> postgresql : Step 15:\nStore queue data

' === Recovery Workflow ===
gap_filter_worker --> queue_service : Step 16:\nRetrieve queue task
queue_service --> postgresql : Step 17:\nRead missing data info
gap_filter_worker --> api_adapter : Step 18:\nVerify and recover data
api_adapter --> krakend : Step 19:\nProxy to KrakenD
krakend --> binance : Step 20:\nRequest Binance data
krakend --> bybit : Step 21:\nRequest Bybit data
binance --> krakend : Step 22:\nReturn Binance data
bybit --> krakend : Step 23:\nReturn Bybit data
krakend --> api_adapter : Step 24:\nReturn raw data
api_adapter --> gap_filter_worker : Step 25:\nReturn merged recovery data
gap_filter_worker --> historical_service : Step 26:\nSave recovery data
historical_service --> postgresql : Step 27:\nPersist data

@enduml
