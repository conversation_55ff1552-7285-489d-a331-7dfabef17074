#!/bin/bash

# Update package lists
echo "Updating package lists..."
sudo apt update -y

# Install nano
echo "Installing nano..."
sudo apt install nano -y

# Install HAProxy
echo "Installing HAProxy..."
sudo apt install haproxy -y

# Enable and start HAProxy
sudo systemctl enable haproxy
sudo systemctl start haproxy

# Install Node.js
echo "Installing Node.js..."
sudo apt install -y ca-certificates curl gnupg
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | sudo gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
NODE_MAJOR=20
echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | sudo tee /etc/apt/sources.list.d/nodesource.list
sudo apt update -y
sudo apt install nodejs -y

# Install Git (bukan github, karena github adalah layanan)
echo "Installing Git..."
sudo apt install git -y

# Konfigurasi Git
echo "Configuring Git..."
read -p "Enter your GitHub name: " github_name
read -p "Enter your GitHub email: " github_email
git config --global user.name "$github_name"
git config --global user.email "$github_email"

# Login ke GitHub (menggunakan SSH)
echo "Setting up GitHub SSH authentication..."
if [ ! -f ~/.ssh/id_ed25519 ]; then
    ssh-keygen -t ed25519 -C "$github_email" -f ~/.ssh/id_ed25519 -N ""
    eval "$(ssh-agent -s)"
    ssh-add ~/.ssh/id_ed25519
    echo "Please add this SSH key to your GitHub account:"
    cat ~/.ssh/id_ed25519.pub
    read -p "Press enter after you've added the SSH key to GitHub..."
fi

# Clone repo
read -p "Enter GitHub repo URL (e.g., **************:user/repo.git): " repo_url
read -p "Enter directory name to clone into (leave empty for default): " dir_name

if [ -z "$dir_name" ]; then
    git clone $repo_url
else
    git clone $repo_url $dir_name
fi

# Update repo
if [ -z "$dir_name" ]; then
    repo_dir=$(basename $repo_url .git)
else
    repo_dir=$dir_name
fi

cd $repo_dir
git pull origin master

# Install dependencies jika ada package.json
if [ -f "package.json" ]; then
    echo "Installing Node.js dependencies..."
    npm install
fi

# Create systemd service
read -p "Do you want to create a systemd service for a Node.js application? (y/n): " create_service

if [[ $create_service =~ ^[Yy]$ ]]; then
    read -p "Enter application entry file (e.g., app.js): " app_entry
    read -p "Enter service name (e.g., myapp): " service_name
    
    # Membuat file service
    service_file="/etc/systemd/system/${service_name}.service"
    
    echo "Creating service file at $service_file..."
    sudo bash -c "cat > $service_file" <<EOF
[Unit]
Description=${service_name} Node.js Application
After=network.target

[Service]
Environment=NODE_PORT=3000
Type=simple
User=$(whoami)
WorkingDirectory=$(pwd)
Environment=NODE_ENV=production
ExecStart=/usr/bin/node ${app_entry}
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd dan start service
    sudo systemctl daemon-reload
    sudo systemctl enable ${service_name}
    sudo systemctl start ${service_name}
    
    echo "Service created and started!"
    echo "You can check status with: systemctl status ${service_name}"
fi

echo "All tasks completed successfully!"