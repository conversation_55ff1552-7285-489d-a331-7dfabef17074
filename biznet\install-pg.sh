#!/bin/bash

# === Variabel Deteksi Otomatis ===
PG_VERSION="15"
CPU_CORES=$(nproc)
TOTAL_MEM_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
TOTAL_MEM_MB=$((TOTAL_MEM_KB / 1024))
TOTAL_MEM_GB=$((TOTAL_MEM_MB / 1024))

# === Estimasi Penggunaan RAM (konservatif) ===
SHARED_BUFFERS_MB=$((TOTAL_MEM_MB / 4))
EFFECTIVE_CACHE_MB=$((TOTAL_MEM_MB * 75 / 100))
WORK_MEM_MB=64
MAINTENANCE_MEM_MB=512

# === Install PostgreSQL ===
echo "[+] Installing PostgreSQL..."
sudo apt update
sudo apt install -y postgresql postgresql-contrib

echo "[+] Stopping PostgreSQL to apply configs..."
sudo systemctl stop postgresql

CONF_DIR="/etc/postgresql/$PG_VERSION/main"
DATA_DIR="/var/lib/postgresql/$PG_VERSION/main"
PG_CONF="$CONF_DIR/postgresql.conf"
HBA_CONF="$CONF_DIR/pg_hba.conf"

# === Update postgresql.conf ===
echo "[+] Configuring postgresql.conf with full resource usage..."

sudo sed -i "s/^#*shared_buffers = .*/shared_buffers = ${SHARED_BUFFERS_MB}MB/" $PG_CONF
sudo sed -i "s/^#*work_mem = .*/work_mem = ${WORK_MEM_MB}MB/" $PG_CONF
sudo sed -i "s/^#*maintenance_work_mem = .*/maintenance_work_mem = ${MAINTENANCE_MEM_MB}MB/" $PG_CONF
sudo sed -i "s/^#*effective_cache_size = .*/effective_cache_size = ${EFFECTIVE_CACHE_MB}MB/" $PG_CONF

sudo sed -i "s/^#*max_connections = .*/max_connections = 100/" $PG_CONF
sudo sed -i "s/^#*wal_buffers = .*/wal_buffers = 16MB/" $PG_CONF
sudo sed -i "s/^#*checkpoint_completion_target = .*/checkpoint_completion_target = 0.9/" $PG_CONF
sudo sed -i "s/^#*default_statistics_target = .*/default_statistics_target = 100/" $PG_CONF
sudo sed -i "s/^#*synchronous_commit = .*/synchronous_commit = off/" $PG_CONF

sudo sed -i "s/^#*max_worker_processes = .*/max_worker_processes = $CPU_CORES/" $PG_CONF
sudo sed -i "s/^#*max_parallel_workers = .*/max_parallel_workers = $CPU_CORES/" $PG_CONF
sudo sed -i "s/^#*max_parallel_workers_per_gather = .*/max_parallel_workers_per_gather = $((CPU_CORES/2))/" $PG_CONF

sudo sed -i "s/^#*listen_addresses = .*/listen_addresses = '*'/" $PG_CONF

# === Update pg_hba.conf ===
echo "[+] Allowing remote access (0.0.0.0/0)..."

sudo bash -c "echo 'host    all             all             0.0.0.0/0               md5' >> $HBA_CONF"

# === Restart PostgreSQL ===
echo "[+] Restarting PostgreSQL..."
sudo systemctl restart postgresql

# === Tes koneksi lokal ===
echo "[+] PostgreSQL version check:"
sudo -u postgres psql -c "SELECT version();"

echo "[✓] PostgreSQL installed and configured to use full VM resources."
