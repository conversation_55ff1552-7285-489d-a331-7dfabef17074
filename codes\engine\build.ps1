$imageName = "damarnururichwan/rt"
$buildNumber = Get-Date -Format "yyyyMMddHHmmss"

# 1. Clean up existing Docker Machine
Write-Host "Performing Docker Machine cleanup..."
docker-machine rm -f default 2>&1 | Out-Null
Get-Process "VBox*" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2  # Give processes time to terminate

# 2. Create new machine
Write-Host "Creating new Docker Machine..."
docker-machine create --driver virtualbox default | Out-Host
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Machine creation failed"
    exit 1
}

# 3. Configure environment - NEW FIXED VERSION
Write-Host "Configuring Docker environment..."
$envOutput = docker-machine env default --shell powershell 2>&1
if ($LASTEXITCODE -eq 0) {
    # Handle the multi-line output properly
    $envOutput | Where-Object { $_ -match "^\s*SET" } | ForEach-Object {
        Invoke-Expression $_
    }
} else {
    Write-Host "ERROR: Environment configuration failed: $envOutput"
    exit 1
}

# 4. Verify connection
Write-Host "Verifying Docker connection..."
docker version 2>&1 | Out-Host
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Docker connection failed"
    exit 1
}

# 5. Build and push
Write-Host "Building Docker image..."
docker build -t "${imageName}:${buildNumber}" -t "${imageName}:latest" .
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Build failed"
    exit 1
}

Write-Host "Pushing images..."
docker push "${imageName}:${buildNumber}"
docker push "${imageName}:latest"

Write-Host "SUCCESS: Built and pushed ${imageName}:${buildNumber} and ${imageName}:latest"