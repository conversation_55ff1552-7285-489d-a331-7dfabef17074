const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting historical cache migration to monthly partitions...');

try {
  // Create dist directory if it doesn't exist
  const distDir = path.join(__dirname, 'dist', 'helper');
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }

  // Compile TypeScript file
  console.log('Compiling migration script...');
  execSync('npx tsc src/helper/migrate-to-monthly-cache.ts --outDir dist --target es2020 --module commonjs --esModuleInterop --skipLibCheck --resolveJsonModule', {
    cwd: __dirname,
    stdio: 'inherit'
  });

  // Check if compiled file exists
  const compiledFile = path.join(__dirname, 'dist', 'helper', 'migrate-to-monthly-cache.js');
  if (!fs.existsSync(compiledFile)) {
    throw new Error('Compilation failed - output file not found');
  }

  // Run migration
  console.log('Running migration...');
  execSync('node dist/helper/migrate-to-monthly-cache.js', {
    cwd: __dirname,
    stdio: 'inherit'
  });

  console.log('Migration completed successfully!');
} catch (error) {
  console.error('Migration failed:', error.message);
  process.exit(1);
}