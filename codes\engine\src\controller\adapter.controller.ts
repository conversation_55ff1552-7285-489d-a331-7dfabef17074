import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { AdapterService } from 'src/services/adapter.service';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { SetLeverageDto } from 'src/dto/set-leverage.dto';
import { PlaceOrderDto } from 'src/dto/place-order.dto';
import { AmendOrderDto } from 'src/dto/amend-order.dto';
import { CancelOrderDto } from 'src/dto/cancel-order.dto';
import { GetActiveOrdersDto } from 'src/dto/get-active-orders.dto';
import { GetOrderHistoryDto } from 'src/dto/get-order-history.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { logDetail } from 'src/util/log-detail.util';
import { ApiKeyAuth } from 'src/decorators/api-key.decorator';

@ApiTags('Adapter API')
@ApiKeyAuth()
@Controller('adapter')
export class AdapterController {
  constructor(
    private readonly adapterService: AdapterService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('/switch-to-hedge-mode')
  @ApiOperation({ summary: 'Switch to Hedge Mode' })
  @ApiResponse({ status: 200, description: 'Switched to hedge mode' })
  async switchToHedgeMode(): Promise<any> {
    try {
      await this.adapterService.switchToHedgeMode();
      return { message: 'Switched to hedge mode' };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to switch to hedge mode',
        logDetail({
          class: 'AdapterController',
          function: 'switchToHedgeMode',
          error: error.stack || message,
        }),
      );
      return { error: message, success: false };
    }
  }

  @Post('/set-leverage')
  @ApiOperation({ summary: 'Set Leverage' })
  @ApiResponse({ status: 200, description: 'Leverage set' })
  async setLeverage(@Body() body: SetLeverageDto): Promise<any> {
    try {
      await this.adapterService.setLeverage(body);
      return { message: 'Leverage set' };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to set leverage',
        logDetail({
          class: 'AdapterController',
          function: 'setLeverage',
          body,
          error: error.stack || message,
        }),
      );
      return { error: message, success: false };
    }
  }

  @Post('/get-wallet-balance')
  @ApiOperation({ summary: 'Get Wallet Balance' })
  @ApiResponse({ status: 200, description: 'Wallet Balance' })
  async getWalletBalance(): Promise<number> {
    try {
      const result = await this.adapterService.getWalletBalance();
      return result ?? 0;
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to get wallet balance',
        logDetail({
          class: 'AdapterController',
          function: 'getWalletBalance',
          error: error.stack || message,
        }),
      );
      return 0;
    }
  }

  @Post('/place-order')
  @ApiOperation({ summary: 'Place Order' })
  @ApiResponse({ status: 200, description: 'Order placed' })
  async placeOrder(@Body() body: PlaceOrderDto): Promise<any> {
    try {
      await this.adapterService.placeOrder(body);
      return { message: 'Order placed' };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to place order',
        logDetail({
          class: 'AdapterController',
          function: 'placeOrder',
          body,
          error: error.stack || message,
        }),
      );
      return { error: message, success: false };
    }
  }

  @Post('/amend-order')
  @ApiOperation({ summary: 'Amend Order' })
  @ApiResponse({ status: 200, description: 'Order amended' })
  async amendOrder(@Body() body: AmendOrderDto): Promise<any> {
    try {
      await this.adapterService.amendOrder(body);
      return { message: 'Order amended' };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to amend order',
        logDetail({
          class: 'AdapterController',
          function: 'amendOrder',
          body,
          error: error.stack || message,
        }),
      );
      return { error: message, success: false };
    }
  }

  @Post('/cancel-order')
  @ApiOperation({ summary: 'Cancel Order' })
  @ApiResponse({ status: 200, description: 'Order canceled' })
  async cancelOrder(@Body() body: CancelOrderDto): Promise<any> {
    try {
      await this.adapterService.cancelOrder(body);
      return { message: 'Order canceled' };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to cancel order',
        logDetail({
          class: 'AdapterController',
          function: 'cancelOrder',
          body,
          error: error.stack || message,
        }),
      );
      return { error: message, success: false };
    }
  }

  @Post('/active-orders')
  @ApiOperation({ summary: 'Get Active Orders' })
  @ApiResponse({
    status: 200,
    description: 'List of active orders',
    isArray: true,
  })
  async getActiveOrders(@Body() body: GetActiveOrdersDto): Promise<any[]> {
    try {
      const result = await this.adapterService.getActiveOrders(body);
      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to get active orders',
        logDetail({
          class: 'AdapterController',
          function: 'getActiveOrders',
          body,
          error: error.stack || message,
        }),
      );
      return [];
    }
  }

  @Post('/order-history')
  @ApiOperation({ summary: 'Get Order History' })
  @ApiResponse({
    status: 200,
    description: 'List of order history',
    isArray: true,
  })
  async getOrderHistory(@Body() body: GetOrderHistoryDto): Promise<any[]> {
    try {
      const result = await this.adapterService.getOrderHistory(body);
      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Failed to get order history',
        logDetail({
          class: 'AdapterController',
          function: 'getOrderHistory',
          body,
          error: error.stack || message,
        }),
      );
      return [];
    }
  }

  @Post('/historical')
  @ApiOperation({ summary: 'Get Historical Data' })
  @ApiResponse({
    status: 200,
    description: 'List of Historical Data',
    isArray: true,
  })
  async fetchBybitHistorical(
    @Body() body: GetHistoricalDto,
  ): Promise<Historical[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result = await this.adapterService.fetchBybitHistorical(body);
      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Historical data fetch failed',
        logDetail({
          class: 'AdapterController',
          function: 'fetchBybitHistorical',
          body,
          error: error.stack || message,
        }),
      );
      return [];
    }
  }

  @Post('/instrument')
  @ApiOperation({ summary: 'Get Instrument Data' })
  @ApiResponse({
    status: 200,
    description: 'List of Instrument Data',
    isArray: true,
  })
  async fetchBybitInstrument(
    @Body() body: GetInstrumentDto,
  ): Promise<Instrument[]> {
    try {
      const result = await this.adapterService.fetchBybitInstrument(body);
      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';
      this.logger.error(
        'Instrument data fetch failed',
        logDetail({
          class: 'AdapterController',
          function: 'fetchBybitInstrument',
          body,
          error: error.stack || message,
        }),
      );
      return [];
    }
  }
}
