import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { BacktestMethodService } from 'src/services/backtest-method.service';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';

@ApiTags('Backtest Method')
@Controller('backtest-method')
export class BacktestMethodController {
  constructor(
    private readonly backtestMethodService: BacktestMethodService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('result')
  @ApiOperation({ summary: 'Generate Backtest Method Result' })
  @ApiResponse({
    status: 200,
    description: 'List of generated backtest method results',
    isArray: true,
  })
  async getResult(@Body() body: GetBacktestMethodDto): Promise<MethodResult[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result =
        await this.backtestMethodService.getBacktestMethodResult(body);
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate result';

      this.logger.error(
        'Failed to generate backtest method result',
        logDetail({
          class: 'BacktestMethodController',
          function: 'getResult',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('performance')
  @ApiOperation({ summary: 'Generate Backtest Performance' })
  async getPerformance(
    @Body() body: GetBacktestMethodDto,
  ): Promise<BacktestPerformance> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const results =
        await this.backtestMethodService.getBacktestMethodResult(body);
      const performance =
        await this.backtestMethodService.getBacktestMethodPerformance(
          body,
          results,
        );
      return performance;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate performance';

      this.logger.error(
        'Failed to generate performance',
        logDetail({
          class: 'AppController',
          function: 'getPerformance',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('result-performance')
  @ApiOperation({ summary: 'Generate Result & Performance from Plan' })
  @ApiResponse({
    status: 200,
    description: 'Combined result and performance from plan input',
    schema: {
      example: {
        result: [
          {
            methodId: 'abc123',
            date: '2025-01-01T00:00:00.000Z',
            status: 'profit',
            openDate: '2025-01-02T00:00:00.000Z',
            closedDate: '2025-01-03T00:00:00.000Z',
          },
        ],
        performance: {
          methodId: 'abc123',
          fromDate: '2025-01-01T00:00:00.000Z',
          endDate: '2025-06-01T00:00:00.000Z',
          totalValidTrade: 23,
          totalInvalidTrade: 3,
          probability: 0.65,
          maxOpenPosition: 3,
          maxConsecutiveLoss: 2,
        },
      },
    },
  })
  async generateBoth(@Body() body: GetBacktestMethodDto): Promise<{
    result: MethodResult[];
    performance: BacktestPerformance;
  }> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const results =
        await this.backtestMethodService.getBacktestMethodResult(body);
      const performance =
        await this.backtestMethodService.getBacktestMethodPerformance(
          body,
          results,
        );
      return { result: results, performance };
    } catch (error: any) {
      const message = error?.message || 'Failed to generate performance';

      this.logger.error(
        'Failed to generate performance',
        logDetail({
          class: 'AppController',
          function: 'getPerformance',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
