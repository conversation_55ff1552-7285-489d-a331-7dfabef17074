import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestMultiMethodService } from 'src/services/backtest-multi-method.service';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';

@ApiTags('Backtest Multi Method')
@Controller('backtest-multi-method')
export class BacktestMultiMethodController {
  constructor(
    private readonly backtestMultiMethodService: BacktestMultiMethodService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('result')
  @ApiOperation({ summary: 'Generate Backtest Result' })
  async getResult(
    @Body() body: GetBacktestMultiMethodDto,
  ): Promise<MethodResult[]> {
    try {
      const result =
        await this.backtestMultiMethodService.getBacktestMultiMethodResult(
          body,
        );
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate result';

      this.logger.error(
        'Failed to generate backtest multi method result',
        logDetail({
          class: 'BacktestMultiMethodController',
          function: 'getResult',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('performance')
  @ApiOperation({ summary: 'Generate Backtest Performance' })
  async getPerformance(
    @Body() body: GetBacktestMultiMethodDto,
  ): Promise<BacktestMultiPerformance> {
    try {
      const performance =
        await this.backtestMultiMethodService.getBacktestMultiMethodPerformance(
          body,
        );
      return performance;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate performance';

      this.logger.error(
        'Failed to generate backtest multi method performance',
        logDetail({
          class: 'BacktestMultiMethodController',
          function: 'getPerformance',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('result-performance')
  @ApiOperation({ summary: 'Generate Result & Performance from Plan' })
  @ApiResponse({
    status: 200,
    description: 'Combined result and performance from plan input',
    schema: {
      example: {
        result: [
          {
            methodId: 'abc123',
            date: '2025-01-01T00:00:00.000Z',
            status: 'profit',
            openDate: '2025-01-02T00:00:00.000Z',
            closedDate: '2025-01-03T00:00:00.000Z',
            interval: '5',
            symbol: 'BTCUSDT',
            orderType: 'long',
            entry: 10000,
            stopLoss: 9000,
            takeProfit: 11000,
            stopPercent: 10,
            profitPercent: 10,
            expiryDate: '2025-01-04T00:00:00.000Z',
          },
        ],
        performance: {
          methodId: 'abc123',
          fromDate: '2025-01-01T00:00:00.000Z',
          endDate: '2025-06-01T00:00:00.000Z',
          totalValidTrade: 23,
          totalInvalidTrade: 3,
          probability: 0.65,
          maxOpenPosition: 3,
          maxConsecutiveLoss: 2,
        },
      },
    },
  })
  async generateBoth(@Body() body: GetBacktestMultiMethodDto): Promise<{
    result: MethodResult[];
    performance: BacktestMultiPerformance;
  }> {
    try {
      const result =
        await this.backtestMultiMethodService.getBacktestMutilMethodBoth(body);
      return result;
    } catch (error: any) {
      const message =
        error?.message || 'Failed to generate both result and performance';

      this.logger.error(
        'Failed to generate both',
        logDetail({
          class: 'BacktestMultiMethodController',
          function: 'generateBoth',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
