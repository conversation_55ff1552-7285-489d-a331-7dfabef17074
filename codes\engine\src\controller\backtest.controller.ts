import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetResultArrayDto } from 'src/dto/get-result-array.dto';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { BacktestService } from 'src/services/backtest.service';
import { logDetail } from 'src/util/log-detail.util';
import { MethodResult } from 'src/interface/method-result.interface';
import { Logger } from 'winston';

@ApiTags('Backtest')
@Controller('backtest')
export class BacktestController {
  constructor(
    private readonly backtestService: BacktestService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('result')
  @ApiOperation({ summary: 'Generate Backtest Result' })
  @ApiResponse({
    status: 200,
    description: 'List of generated method results',
    isArray: true,
  })
  async getResult(@Body() body: GetResultArrayDto): Promise<MethodResult[]> {
    try {
      const result = await this.backtestService.getMultiSymbolResult(body.data);
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate result';

      this.logger.error(
        'Failed to generate result',
        logDetail({
          class: 'AppController',
          function: 'getResult',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('performance')
  @ApiOperation({ summary: 'Generate Backtest Performance' })
  @ApiResponse({
    status: 200,
    description: 'Backtest performance result',
    schema: {
      example: {
        methodId: 'abc123',
        fromDate: '2025-01-01T00:00:00.000Z',
        endDate: '2025-06-01T00:00:00.000Z',
        totalValidTrade: 23,
        totalInvalidTrade: 3,
        probability: 0.65,
        maxOpenPosition: 3,
        maxConsecutiveLoss: 2,
      },
    },
  })
  async getPerformance(
    @Body() body: GetResultArrayDto,
  ): Promise<BacktestPerformance> {
    try {
      const result = await this.backtestService.getMultiSymbolResult(body.data);
      const performance = await this.backtestService.getPerformance(result);
      return performance;
    } catch (error: any) {
      const message = error?.message || 'Failed to generate performance';

      this.logger.error(
        'Failed to generate performance',
        logDetail({
          class: 'AppController',
          function: 'getPerformance',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('result-performance')
  @ApiOperation({ summary: 'Generate Result & Performance from Plan' })
  @ApiResponse({
    status: 200,
    description: 'Combined result and performance from plan input',
    schema: {
      example: {
        result: [
          {
            methodId: 'abc123',
            date: '2025-01-01T00:00:00.000Z',
            status: 'profit',
            openDate: '2025-01-02T00:00:00.000Z',
            closedDate: '2025-01-03T00:00:00.000Z',
          },
        ],
        performance: {
          methodId: 'abc123',
          fromDate: '2025-01-01T00:00:00.000Z',
          endDate: '2025-06-01T00:00:00.000Z',
          totalValidTrade: 23,
          totalInvalidTrade: 3,
          probability: 0.65,
          maxOpenPosition: 3,
          maxConsecutiveLoss: 2,
        },
      },
    },
  })
  async generateBoth(@Body() body: GetResultArrayDto): Promise<{
    result: MethodResult[];
    performance: BacktestPerformance;
  }> {
    try {
      const result = await this.backtestService.getMultiSymbolResult(body.data);
      const performance = await this.backtestService.getPerformance(result);
      return { result, performance };
    } catch (error: any) {
      const message =
        error?.message || 'Failed to generate both result and performance';

      this.logger.error(
        'Failed to generate both',
        logDetail({
          class: 'AppController',
          function: 'generateBoth',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
