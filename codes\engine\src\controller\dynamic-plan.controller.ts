import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { DynamicPlanService } from 'src/services/dynamic-plan.service';
import { GetDynamicPlanDto } from 'src/dto/get-dynamic-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { logDetail } from 'src/util/log-detail.util';

@ApiTags('Dynamic Plan')
@Controller('dynamic-plan')
export class DynamicPlanController {
  constructor(
    private readonly appService: DynamicPlanService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Dynamic Plan' })
  @ApiResponse({
    status: 200,
    description: 'List of Dynamic Plan Data',
    isArray: true,
  })
  async getDynamicPlan(@Body() body: GetDynamicPlanDto): Promise<Plan[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result = await this.appService.getPlan(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Dynamic plan data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'getDynamicPlan',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
