import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { logDetail } from 'src/util/log-detail.util';
import { HealthCheckService } from 'src/services/health-check.service';

@ApiTags('Health')
@Controller('health')
export class HealthCheckController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Get('/live')
  @ApiOperation({ summary: 'Liveness probe - checks if application is running' })
  @ApiResponse({ status: 200, description: 'Application is alive' })
  async liveness(): Promise<{ status: string; timestamp: string }> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('/ready')
  @ApiOperation({ summary: 'Readiness probe - checks if application is ready to serve traffic' })
  @ApiResponse({ status: 200, description: 'Application is ready' })
  @ApiResponse({ status: 503, description: 'Application is not ready' })
  async readiness(): Promise<{
    status: string;
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
    message?: string;
  }> {
    const { timestamp, results } = await this.healthCheckService.checkReadiness();

    const hasUnreachable = Object.values(results).includes('unreachable');

    if (hasUnreachable) {
      this.logger.warn(
        'Some services are unreachable',
        logDetail({
          class: 'HealthCheckController',
          function: 'readiness',
          results,
        }),
      );

      throw new HttpException(
        {
          status: 'error',
          message: 'Some services are unreachable',
          timestamp,
          results,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    return {
      status: 'ok',
      timestamp,
      results,
    };
  }

  @Get('/startup')
  @ApiOperation({ summary: 'Startup probe - checks if application has started successfully' })
  @ApiResponse({ status: 200, description: 'Application has started' })
  @ApiResponse({ status: 503, description: 'Application is still starting' })
  async startup(): Promise<{
    status: string;
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    const { timestamp, results } = await this.healthCheckService.checkStartup();

    const hasUnreachable = Object.values(results).includes('unreachable');

    if (hasUnreachable) {
      throw new HttpException(
        {
          status: 'error',
          message: 'Application is still starting',
          timestamp,
          results,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    return {
      status: 'ok',
      timestamp,
      results,
    };
  }

  // Backward compatibility
  @Get('/')
  @ApiOperation({ summary: 'General health check' })
  async check(): Promise<{
    status: string;
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    return this.readiness();
  }
}
