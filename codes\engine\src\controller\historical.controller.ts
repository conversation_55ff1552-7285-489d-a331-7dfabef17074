import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
  Get,
  Query,
  Delete,
} from '@nestjs/common';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { logDetail } from 'src/util/log-detail.util';
import { Historical } from 'src/interface/historical.interface';
import { HistoricalCacheService } from 'src/services/historical-cache.service';

@ApiTags('Historical Data')
@Controller('historical')
export class HistoricalController {
  constructor(
    private readonly historicalCacheService: HistoricalCacheService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Historical Data from Database' })
  @ApiResponse({
    status: 200,
    description: 'List of Historical Data',
    isArray: true,
  })
  async get(@Body() body: GetHistoricalDto): Promise<Historical[]> {
    try {
      // Validasi tanggal jika diperlukan
      body.start = new Date(body.start);
      body.end = new Date(body.end);

      const result = await this.historicalCacheService.getHistorical(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Historical data fetch failed',
        logDetail({
          class: 'HistoricalController',
          function: 'get',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('cache/months')
  @ApiOperation({ summary: 'Get Available Cache Months' })
  @ApiResponse({
    status: 200,
    description: 'List of available cache months',
    isArray: true,
  })
  async getAvailableMonths(
    @Query('symbol') symbol: string,
    @Query('interval') interval: string,
  ): Promise<string[]> {
    try {
      if (!symbol || !interval) {
        throw new HttpException(
          'Symbol and interval are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.historicalCacheService.getAvailableMonths(symbol, interval);
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Failed to get available months',
        logDetail({
          class: 'HistoricalController',
          function: 'getAvailableMonths',
          param: { symbol, interval },
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('cache/info')
  @ApiOperation({ summary: 'Get Cache Information' })
  @ApiResponse({
    status: 200,
    description: 'Cache information including months, records count, and date range',
  })
  async getCacheInfo(
    @Query('symbol') symbol: string,
    @Query('interval') interval: string,
  ): Promise<{
    totalMonths: number;
    months: string[];
    totalRecords: number;
    oldestRecord?: Date;
    newestRecord?: Date;
  }> {
    try {
      if (!symbol || !interval) {
        throw new HttpException(
          'Symbol and interval are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.historicalCacheService.getCacheInfo(symbol, interval);
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Failed to get cache info',
        logDetail({
          class: 'HistoricalController',
          function: 'getCacheInfo',
          param: { symbol, interval },
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Delete('cache/month')
  @ApiOperation({ summary: 'Delete Specific Month Cache' })
  @ApiResponse({
    status: 200,
    description: 'Month cache deleted successfully',
  })
  async deleteMonthCache(
    @Query('symbol') symbol: string,
    @Query('interval') interval: string,
    @Query('yearMonth') yearMonth: string,
  ): Promise<{ message: string }> {
    try {
      if (!symbol || !interval || !yearMonth) {
        throw new HttpException(
          'Symbol, interval, and yearMonth are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.historicalCacheService.deleteHistorical({
        symbol,
        interval,
        yearMonth,
      });

      return { message: `Cache for ${symbol}-${interval}-${yearMonth} deleted successfully` };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Failed to delete month cache',
        logDetail({
          class: 'HistoricalController',
          function: 'deleteMonthCache',
          param: { symbol, interval, yearMonth },
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('cache/cleanup')
  @ApiOperation({ summary: 'Cleanup Empty Month Files' })
  @ApiResponse({
    status: 200,
    description: 'Number of cleaned up files',
  })
  async cleanupEmptyMonths(
    @Query('symbol') symbol: string,
    @Query('interval') interval: string,
  ): Promise<{ cleanedCount: number }> {
    try {
      if (!symbol || !interval) {
        throw new HttpException(
          'Symbol and interval are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const cleanedCount = await this.historicalCacheService.cleanupEmptyMonths(
        symbol,
        interval,
      );

      return { cleanedCount };
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Failed to cleanup empty months',
        logDetail({
          class: 'HistoricalController',
          function: 'cleanupEmptyMonths',
          param: { symbol, interval },
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
