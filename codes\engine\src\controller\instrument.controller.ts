import {
  Controller,
  Get,
  Query,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { InstrumentService } from 'src/services/instrument.service';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';

@ApiTags('Instrument Data')
@Controller('instrument')
export class InstrumentController {
  constructor(
    private readonly instrumentService: InstrumentService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Instrument Data from Bybit' })
  @ApiResponse({
    status: 200,
    description: 'List of Instrument Data',
    isArray: true,
  })
  async get(@Body() body: GetInstrumentDto): Promise<InstrumentEntity[]> {
    try {
      const result = await this.instrumentService.getInstrument(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Instrument data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'fetchBybitInstrument',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
