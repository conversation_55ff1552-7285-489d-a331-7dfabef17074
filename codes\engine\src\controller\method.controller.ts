import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
  Get,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { MethodParamEntity } from 'src/entity/method-param.entity';
import { MethodResultEntity } from 'src/entity/method-result.entity';
import { MethodService } from 'src/services/method.service';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { GetMethodDto } from 'src/dto/get-method.dto';

@ApiTags('Method')
@Controller('method')
export class MethodController {
  constructor(
    private readonly methodService: MethodService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('param')
  @ApiOperation({ summary: 'Get Method Param' })
  @ApiResponse({
    status: 200,
    description: 'List of Method Param',
    isArray: true,
  })
  async getParam(@Body() body: GetMethodDto): Promise<MethodParamEntity[]> {
    try {
      const result = await this.methodService.getParam(body);
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to get method param';

      this.logger.error(
        'Failed to get method param',
        logDetail({
          class: 'MethodController',
          function: 'getParam',
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('result')
  @ApiOperation({ summary: 'Get Method Result' })
  @ApiResponse({
    status: 200,
    description: 'List of Method Result',
    isArray: true,
  })
  async getResult(@Body() body: GetMethodDto): Promise<MethodResultEntity[]> {
    try {
      const result = await this.methodService.getResult(body);
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to get method result';

      this.logger.error(
        'Failed to get method result',
        logDetail({
          class: 'MethodController',
          function: 'getResult',
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('performance')
  @ApiOperation({ summary: 'Get Method Performance' })
  @ApiResponse({
    status: 200,
    description: 'List of Method Performance',
    isArray: true,
  })
  async getPerformance(
    @Body() body: GetMethodDto,
  ): Promise<MethodPerformanceEntity[]> {
    try {
      const result = await this.methodService.getPerformance(body);
      return result;
    } catch (error: any) {
      const message = error?.message || 'Failed to get method performance';

      this.logger.error(
        'Failed to get method performance',
        logDetail({
          class: 'MethodController',
          function: 'getPerformance',
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
