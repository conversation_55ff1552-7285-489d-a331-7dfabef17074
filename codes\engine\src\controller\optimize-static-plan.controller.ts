import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { OptimizedStaticPlan } from 'src/interface/optimized-static-plan.interface';
import { Plan } from 'src/interface/plan.interface';
import { OptimizeStaticPlanService } from 'src/services/optimize-static-plan.service';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';

@ApiTags('Optimize Static Plan')
@Controller('optimize-static-plan')
export class OptimizeStaticPlanController {
  constructor(
    private readonly appService: OptimizeStaticPlanService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('optimized-param')
  @ApiOperation({ summary: 'Get Optimize Param' })
  @ApiResponse({
    status: 200,
    description: 'List of Optimize Plan Data',
    isArray: true,
  })
  async getOptimizedParam(
    @Body() body: GetStaticPlanDto,
  ): Promise<OptimizedStaticPlan> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result = await this.appService.optimize(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Optimize plan data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'getOptimizedParam',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('optimized-plan')
  @ApiOperation({ summary: 'Get Optimized Static Plan' })
  @ApiResponse({
    status: 200,
    description: 'List of Optimized Static Plan Data',
    isArray: true,
  })
  async getOptimizedStaticPlan(
    @Body() body: GetStaticPlanDto,
  ): Promise<Plan[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result = await this.appService.getOptimizedStaticPlan(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Optimize plan data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'getOptimizedStaticPlan',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
