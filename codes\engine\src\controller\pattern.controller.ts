import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { GetPatternDto } from 'src/dto/get-pattern.dto';
import { Pattern } from 'src/interface/pattern.interface';
import { PatternService } from 'src/services/pattern.service';
import { Logger } from 'winston';

@ApiTags('Pattern')
@Controller('pattern')
export class PatternController {
  constructor(
    private readonly appService: PatternService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Pattern' })
  @ApiResponse({
    status: 200,
    description: 'List of Pattern Data',
    isArray: true,
  })
  async getPattern(@Body() body: GetPatternDto): Promise<Pattern[]> {
    try {
      // Validasi tanggal jika diperlukan
      body.start = new Date(body.start);
      body.end = new Date(body.end);

      const result = await this.appService.getPattern(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Pattern data fetch failed',
        logDetail({
          class: 'PatternController',
          function: 'getPattern',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
