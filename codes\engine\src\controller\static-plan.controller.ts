import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { StaticPlanService } from 'src/services/static-plan.service';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';

@ApiTags('Static Plan')
@Controller('static-plan')
export class StaticPlanController {
  constructor(
    private readonly staticPlanService: StaticPlanService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Static Plan' })
  @ApiResponse({
    status: 200,
    description: 'List of Static Plan Data',
    isArray: true,
  })
  async getStaticPlan(@Body() body: GetStaticPlanDto): Promise<Plan[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);
      const result = await this.staticPlanService.getPlan(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Static plan data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'getStaticPlan',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
