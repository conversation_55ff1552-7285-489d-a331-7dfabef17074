import { UseGuards, applyDecorators } from '@nestjs/common';
import { Api<PERSON>earerAuth } from '@nestjs/swagger';
import { ApiKeyGuard } from 'src/guards/api-key.guard';

/**
 * Decorator that applies API key authentication to a controller or method.
 * This decorator:
 * 1. Applies the ApiKeyGuard to validate the API key
 * 2. Adds Swagger documentation for Bearer token authentication
 *
 * Usage:
 * @ApiKeyAuth()
 * @Controller('adapter')
 * export class AdapterController { ... }
 *
 * Or on individual methods:
 * @ApiKeyAuth()
 * @Post('/place-order')
 * async placeOrder() { ... }
 *
 * Authentication:
 * - Use Authorization header: "Bearer your-api-key"
 */
export function ApiKeyAuth() {
  return applyDecorators(UseGuards(ApiKeyGuard), ApiBearerAuth());
}
