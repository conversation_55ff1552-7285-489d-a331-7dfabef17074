import {
  IsString,
  <PERSON>NotEmpty,
  IsIn,
  IsOptional,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for amending orders on Bybit
 * 
 * This DTO is used to validate and document the parameters required
 * for amending existing orders through Bybit API v5.
 * 
 * @example
 * {
 *   "category": "linear",
 *   "symbol": "BTCUSDT",
 *   "orderId": "1234567890",
 *   "qty": "0.02",
 *   "price": "51000"
 * }
 */
export class AmendOrderDto {
  @ApiProperty({
    description: 'Product category',
    example: 'linear',
    enum: ['spot', 'linear', 'inverse', 'option'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['spot', 'linear', 'inverse', 'option'])
  category: 'spot' | 'linear' | 'inverse' | 'option';

  @ApiProperty({
    description: 'Symbol name',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiPropertyOptional({
    description: 'Order ID. Either orderId or orderLinkId is required',
    example: '1234567890',
  })
  @IsOptional()
  @IsString()
  orderId?: string;

  @ApiPropertyOptional({
    description: 'User customised order ID. Either orderId or orderLinkId is required',
    example: 'my-order-001',
  })
  @IsOptional()
  @IsString()
  orderLinkId?: string;

  @ApiPropertyOptional({
    description: 'Implied volatility',
    example: '0.1',
  })
  @IsOptional()
  @IsString()
  orderIv?: string;

  @ApiPropertyOptional({
    description: 'Trigger price',
    example: '51000',
  })
  @IsOptional()
  @IsString()
  triggerPrice?: string;

  @ApiPropertyOptional({
    description: 'Order quantity',
    example: '0.02',
  })
  @IsOptional()
  @IsString()
  qty?: string;

  @ApiPropertyOptional({
    description: 'Order price',
    example: '51000',
  })
  @IsOptional()
  @IsString()
  price?: string;

  @ApiPropertyOptional({
    description: 'Take profit price',
    example: '55000',
  })
  @IsOptional()
  @IsString()
  takeProfit?: string;

  @ApiPropertyOptional({
    description: 'Stop loss price',
    example: '45000',
  })
  @IsOptional()
  @IsString()
  stopLoss?: string;

  @ApiPropertyOptional({
    description: 'Take profit trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  tpTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Stop loss trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  slTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  triggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Take profit limit price',
    example: '54000',
  })
  @IsOptional()
  @IsString()
  tpLimitPrice?: string;

  @ApiPropertyOptional({
    description: 'Stop loss limit price',
    example: '46000',
  })
  @IsOptional()
  @IsString()
  slLimitPrice?: string;
}
