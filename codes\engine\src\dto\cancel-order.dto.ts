import {
  IsString,
  IsNotEmpty,
  IsIn,
  IsOptional,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for cancelling orders on Bybit
 * 
 * This DTO is used to validate and document the parameters required
 * for cancelling existing orders through Bybit API v5.
 * 
 * @example
 * {
 *   "category": "linear",
 *   "symbol": "BTCUSDT",
 *   "orderId": "1234567890"
 * }
 */
export class CancelOrderDto {
  @ApiProperty({
    description: 'Product category',
    example: 'linear',
    enum: ['spot', 'linear', 'inverse', 'option'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['spot', 'linear', 'inverse', 'option'])
  category: 'spot' | 'linear' | 'inverse' | 'option';

  @ApiProperty({
    description: 'Symbol name',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiPropertyOptional({
    description: 'Order ID. Either orderId or orderLinkId is required',
    example: '1234567890',
  })
  @IsOptional()
  @IsString()
  orderId?: string;

  @ApiPropertyOptional({
    description: 'User customised order ID. Either orderId or orderLinkId is required',
    example: 'my-order-001',
  })
  @IsOptional()
  @IsString()
  orderLinkId?: string;

  @ApiPropertyOptional({
    description: 'Order filter',
    example: 'Order',
    enum: ['Order', 'tpslOrder', 'StopOrder'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Order', 'tpslOrder', 'StopOrder'])
  orderFilter?: 'Order' | 'tpslOrder' | 'StopOrder';
}
