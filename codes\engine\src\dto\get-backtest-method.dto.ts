import { IsBoolean, <PERSON>NotEmpty, IsString } from 'class-validator';
import { GetDynamicPlanDto } from './get-dynamic-plan.dto';
import { MethodType } from 'src/interface/method-type.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetBacktestMethodDto extends GetDynamicPlanDto {
  @ApiPropertyOptional({
    description: 'Unique ID of the method',
  })
  @ApiProperty({
    description: 'Method Type',
    example: 'static',
  })
  @IsString()
  @IsNotEmpty()
  methodType: MethodType;

  @ApiPropertyOptional({
    description: 'Enable Otimization',
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  enableOptimization?: boolean;
}
