import { IsB<PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GetBacktestMultiMethodDto {
  @ApiProperty({
    description: 'Minimum Probability',
    example: 70,
  })
  @IsNumber()
  @IsNotEmpty()
  minProbability: number;

  @ApiProperty({
    description: 'Limit of method data to return (e.g., 100)',
    example: 1000,
  })
  @IsNumber()
  @IsNotEmpty()
  methodLimit: number;

  @ApiProperty({
    description: 'Only return pending results',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  pendingResultOnly?: boolean;
}
