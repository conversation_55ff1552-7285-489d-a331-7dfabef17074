import { IsDateString, <PERSON>NotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator';
import configurations from '../configurations';
import { ApiProperty } from '@nestjs/swagger';

export class GetHistoricalDto {
  
  @ApiProperty({
    description: 'Trading symbol (e.g., BTCUSDT)',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiProperty({
    description: 'Time interval (e.g., 1, 3, 5, 15)',
    example: '60',
    enum: configurations('INTERVALS'),
  })
  @IsString()
  @IsNotEmpty()
  interval: string;

  @ApiProperty({
    description: 'Start date in ISO format',
    example: '2023-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  start: Date;

  @ApiProperty({
    description: 'End date in ISO format',
    example: '2023-01-31T23:59:59Z',
  })
  @IsDateString()
  @IsNotEmpty()
  end: Date;

  @ApiProperty({
    description: 'Limit of data to return (e.g., 100)',
    example: 10000000000,
  })
  @IsNumber()
  @IsNotEmpty()
  limit: number;

  @ApiProperty({
    description: 'Sort of data to return (e.g., DESC)',
    example: 'ASC',
    enum: ['DESC', 'ASC'],
  })
  @IsString()
  @IsNotEmpty()
  sort: 'ASC' | 'DESC';
}
