import {
  IsString,
  <PERSON>NotEmpty,
  IsIn,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * DTO for getting order history from Bybit
 * 
 * This DTO is used to validate and document the parameters required
 * for retrieving order history through Bybit API v5.
 * 
 * @example
 * {
 *   "category": "linear",
 *   "symbol": "BTCUSDT",
 *   "limit": 20
 * }
 */
export class GetOrderHistoryDto {
  @ApiProperty({
    description: 'Product category',
    example: 'linear',
    enum: ['spot', 'linear', 'inverse', 'option'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['spot', 'linear', 'inverse', 'option'])
  category: 'spot' | 'linear' | 'inverse' | 'option';

  @ApiPropertyOptional({
    description: 'Symbol name. If not passed, returns orders for all symbols',
    example: 'BTCUSDT',
  })
  @IsOptional()
  @IsString()
  symbol?: string;

  @ApiPropertyOptional({
    description: 'Base coin. For option only',
    example: 'BTC',
  })
  @IsOptional()
  @IsString()
  baseCoin?: string;

  @ApiPropertyOptional({
    description: 'Settle coin. For linear & inverse only',
    example: 'USDT',
  })
  @IsOptional()
  @IsString()
  settleCoin?: string;

  @ApiPropertyOptional({
    description: 'Order ID',
    example: '1234567890',
  })
  @IsOptional()
  @IsString()
  orderId?: string;

  @ApiPropertyOptional({
    description: 'User customised order ID',
    example: 'my-order-001',
  })
  @IsOptional()
  @IsString()
  orderLinkId?: string;

  @ApiPropertyOptional({
    description: 'Order status',
    example: 'Filled',
    enum: [
      'Created',
      'New', 
      'Rejected',
      'PartiallyFilled',
      'PartiallyFilledCanceled',
      'Filled',
      'Cancelled',
      'Untriggered',
      'Triggered',
      'Deactivated',
      'Active'
    ],
  })
  @IsOptional()
  @IsString()
  @IsIn([
    'Created',
    'New',
    'Rejected', 
    'PartiallyFilled',
    'PartiallyFilledCanceled',
    'Filled',
    'Cancelled',
    'Untriggered',
    'Triggered',
    'Deactivated',
    'Active'
  ])
  orderStatus?: 'Created' | 'New' | 'Rejected' | 'PartiallyFilled' | 'PartiallyFilledCanceled' | 'Filled' | 'Cancelled' | 'Untriggered' | 'Triggered' | 'Deactivated' | 'Active';

  @ApiPropertyOptional({
    description: 'Order filter',
    example: 'Order',
    enum: ['Order', 'tpslOrder', 'StopOrder'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Order', 'tpslOrder', 'StopOrder'])
  orderFilter?: 'Order' | 'tpslOrder' | 'StopOrder';

  @ApiPropertyOptional({
    description: 'Limit for data size per page. [1, 50]. Default: 20',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  limit?: number;

  @ApiPropertyOptional({
    description: 'Cursor. Use the nextPageCursor token from the response to retrieve the next page of the result set',
    example: 'cursor_token_here',
  })
  @IsOptional()
  @IsString()
  cursor?: string;
}
