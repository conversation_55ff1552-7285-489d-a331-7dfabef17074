import { ApiProperty } from "@nestjs/swagger";
import { GetHistoricalDto } from "./get-historical.dto";
import { IsNotEmpty, IsString } from "class-validator";
import configurations from "../configurations";

export class GetPatternDto extends GetHistoricalDto {
  @ApiProperty({
    description: 'Pattern Trend (e.g., up, down)',
    example: 'up',
    enum: ["up", "down", "neutral"],
  })
  @IsString()
  @IsNotEmpty()
  trend: string;
  
  @ApiProperty({
    description: 'Pattern Types (e.g., candlestick, chart, indicator, etc.)',
    example: 'candlestick',
    enum: configurations('PATTERN_TYPES'),
  })
  @IsString()
  @IsNotEmpty()
  patternType: string;

  @ApiProperty({
    description: 'Candlestick Pattern (e.g., hammer, bullish_engulfing, etc.)',
    example: 'bullish_engulfing',
    enum: configurations('CANDLESTICK_PATTERNS'),
  })
  @IsString()
  @IsNotEmpty()
  pattern: string;
}