import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class GetResultDto {
  @ApiProperty({
    description: 'Unique ID of the plan',
    example: 'BTCUSDT-5-2025-01-01T00:00:00.000Z',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Date of the plan',
    example: '2025-01-01T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  @IsDate()
  @Type(() => Date)
  date: Date;

  @ApiProperty({
    description: 'Time interval',
    example: '5',
  })
  @IsString()
  @IsNotEmpty()
  interval: string;

  @ApiProperty({
    description: 'Trading symbol',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiProperty({
    description: 'Order type (e.g., long or short)',
    example: 'long',
  })
  @IsString()
  @IsNotEmpty()
  orderType: string;

  @ApiProperty({
    description: 'Entry percent by close',
    example: -0.5,
  })
  @IsNumber()
  entryPercentByClose: number;

  @ApiProperty({
    description: 'Risk percent per trade',
    example: -2.0,
  })
  @IsNumber()
  riskPercent: number;

  @ApiProperty({
    description: 'Reward percent per trade',
    example: 4.0,
  })
  @IsNumber()
  rewardPercent: number;

  @ApiProperty({
    description: 'Period in samples for plan validity',
    example: 5,
  })
  @IsNumber()
  validityPeriod: number;

  @ApiProperty({
    description: 'Entry price',
    example: 93800.5,
  })
  @IsNumber()
  entry: number;

  @ApiProperty({
    description: 'Stop loss price',
    example: 92700.0,
  })
  @IsNumber()
  stopLoss: number;

  @ApiProperty({
    description: 'Take profit price',
    example: 96500.0,
  })
  @IsNumber()
  takeProfit: number;

  @ApiProperty({
    description: 'Stop percent',
    example: -1.5,
  })
  @IsNumber()
  stopPercent: number;

  @ApiProperty({
    description: 'Profit percent',
    example: 2.8,
  })
  @IsNumber()
  profitPercent: number;

  @ApiProperty({
    description: 'Expiry date for the plan',
    example: '2025-01-10T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  @IsDate()
  @Type(() => Date)
  expiryDate: Date;

  @ApiProperty({
    description: 'Pattern type (e.g., candlestick)',
    example: 'candlestick',
  })
  @IsString()
  @IsNotEmpty()
  patternType: string;

  @ApiProperty({
    description: 'Pattern name (e.g., hammer, engulfing)',
    example: 'hammer',
  })
  @IsString()
  @IsNotEmpty()
  pattern: string;
}
