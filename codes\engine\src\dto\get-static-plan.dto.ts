import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, IsArray } from 'class-validator';
import configurations from 'src/configurations';
import { GetPatternDto } from './get-pattern.dto';

export class GetStaticPlanDto extends GetPatternDto {
  @ApiProperty({
    description: 'Method ID',
  })
  @IsString()
  methodId?: string;

  @ApiProperty({
    description: 'Order type for backtesting (e.g., long, short)',
    example: 'long',
    enum: configurations('ORDER_TYPES'),
  })
  @IsString()
  @IsNotEmpty()
  orderType: string;

  @ApiProperty({
    description: 'Number of samples to validate plan validity',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  validityPeriod: number;

  @ApiProperty({
    description: 'Percentage of entry by close per trade',
    example: -0.5,
  })
  @IsNumber()
  @IsNotEmpty()
  entryPercentByClose: number;

  @ApiProperty({
    description: 'Percentage of risk per trade',
    example: -1,
  })
  @IsNumber()
  @IsNotEmpty()
  riskPercent: number;

  @ApiProperty({
    description: 'Percentage of reward per trade',
    example: 2,
  })
  @IsNumber()
  @IsNotEmpty()
  rewardPercent: number;
}
