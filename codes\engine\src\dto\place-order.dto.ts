import {
  IsString,
  IsNotEmpty,
  IsIn,
  IsO<PERSON>al,
  IsN<PERSON>ber,
  IsBoolean,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * DTO for placing orders on Bybit
 * 
 * This DTO is used to validate and document the parameters required
 * for placing orders through Bybit API v5.
 * 
 * @example
 * {
 *   "category": "linear",
 *   "symbol": "BTCUSDT",
 *   "side": "Buy",
 *   "orderType": "Market",
 *   "qty": "0.01"
 * }
 */
export class PlaceOrderDto {
  @ApiProperty({
    description: 'Product category',
    example: 'linear',
    enum: ['spot', 'linear', 'inverse', 'option'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['spot', 'linear', 'inverse', 'option'])
  category: 'spot' | 'linear' | 'inverse' | 'option';

  @ApiProperty({
    description: 'Symbol name',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiPropertyOptional({
    description: 'Whether to borrow. Unified spot only. 0(default): false, 1: true',
    example: 0,
    enum: [0, 1],
  })
  @IsOptional()
  @IsNumber()
  @IsIn([0, 1])
  @Transform(({ value }) => Number(value))
  isLeverage?: 0 | 1;

  @ApiProperty({
    description: 'Buy or Sell',
    example: 'Buy',
    enum: ['Buy', 'Sell'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['Buy', 'Sell'])
  side: 'Buy' | 'Sell';

  @ApiProperty({
    description: 'Order type',
    example: 'Market',
    enum: ['Market', 'Limit'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['Market', 'Limit'])
  orderType: 'Market' | 'Limit';

  @ApiProperty({
    description: 'Order quantity',
    example: '0.01',
  })
  @IsString()
  @IsNotEmpty()
  qty: string;

  @ApiPropertyOptional({
    description: 'The unit for qty when creating Spot market orders for UTA account',
    example: 'baseCoin',
    enum: ['baseCoin', 'quoteCoin'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['baseCoin', 'quoteCoin'])
  marketUnit?: 'baseCoin' | 'quoteCoin';

  @ApiPropertyOptional({
    description: 'Slippage tolerance type',
    example: 'percentage',
  })
  @IsOptional()
  @IsString()
  slippageToleranceType?: string;

  @ApiPropertyOptional({
    description: 'Slippage tolerance',
    example: '0.1',
  })
  @IsOptional()
  @IsString()
  slippageTolerance?: string;

  @ApiPropertyOptional({
    description: 'Order price. Required for Limit orders',
    example: '50000',
  })
  @IsOptional()
  @IsString()
  price?: string;

  @ApiPropertyOptional({
    description: 'Trigger direction. 1: rise, 2: fall',
    example: 1,
    enum: [1, 2],
  })
  @IsOptional()
  @IsNumber()
  @IsIn([1, 2])
  @Transform(({ value }) => Number(value))
  triggerDirection?: 1 | 2;

  @ApiPropertyOptional({
    description: 'Order filter',
    example: 'Order',
    enum: ['Order', 'tpslOrder', 'StopOrder'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Order', 'tpslOrder', 'StopOrder'])
  orderFilter?: 'Order' | 'tpslOrder' | 'StopOrder';

  @ApiPropertyOptional({
    description: 'Trigger price',
    example: '51000',
  })
  @IsOptional()
  @IsString()
  triggerPrice?: string;

  @ApiPropertyOptional({
    description: 'Trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  triggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Implied volatility',
    example: '0.1',
  })
  @IsOptional()
  @IsString()
  orderIv?: string;

  @ApiPropertyOptional({
    description: 'Time in force',
    example: 'GTC',
    enum: ['GTC', 'IOC', 'FOK', 'PostOnly', 'RPI'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['GTC', 'IOC', 'FOK', 'PostOnly', 'RPI'])
  timeInForce?: 'GTC' | 'IOC' | 'FOK' | 'PostOnly' | 'RPI';

  @ApiPropertyOptional({
    description: 'Position index. 0: one-way mode, 1: hedge-mode Buy side, 2: hedge-mode Sell side',
    example: 0,
    enum: [0, 1, 2],
  })
  @IsOptional()
  @IsNumber()
  @IsIn([0, 1, 2])
  @Transform(({ value }) => Number(value))
  positionIdx?: 0 | 1 | 2;

  @ApiPropertyOptional({
    description: 'User customised order ID',
    example: 'my-order-001',
  })
  @IsOptional()
  @IsString()
  orderLinkId?: string;

  @ApiPropertyOptional({
    description: 'Take profit price',
    example: '55000',
  })
  @IsOptional()
  @IsString()
  takeProfit?: string;

  @ApiPropertyOptional({
    description: 'Stop loss price',
    example: '45000',
  })
  @IsOptional()
  @IsString()
  stopLoss?: string;

  @ApiPropertyOptional({
    description: 'Take profit trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  tpTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Stop loss trigger price type',
    example: 'LastPrice',
    enum: ['LastPrice', 'IndexPrice', 'MarkPrice'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['LastPrice', 'IndexPrice', 'MarkPrice'])
  slTriggerBy?: 'LastPrice' | 'IndexPrice' | 'MarkPrice';

  @ApiPropertyOptional({
    description: 'Reduce only. true means your position can only reduce in size if this order is triggered',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  reduceOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Close on trigger',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  closeOnTrigger?: boolean;

  @ApiPropertyOptional({
    description: 'SMP execution type',
    example: 'None',
    enum: ['None', 'CancelMaker', 'CancelTaker', 'CancelBoth'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['None', 'CancelMaker', 'CancelTaker', 'CancelBoth'])
  smpType?: 'None' | 'CancelMaker' | 'CancelTaker' | 'CancelBoth';

  @ApiPropertyOptional({
    description: 'Market maker protection',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  mmp?: boolean;

  @ApiPropertyOptional({
    description: 'TP/SL mode',
    example: 'Full',
    enum: ['Full', 'Partial'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Full', 'Partial'])
  tpslMode?: 'Full' | 'Partial';

  @ApiPropertyOptional({
    description: 'Take profit limit price',
    example: '54000',
  })
  @IsOptional()
  @IsString()
  tpLimitPrice?: string;

  @ApiPropertyOptional({
    description: 'Stop loss limit price',
    example: '46000',
  })
  @IsOptional()
  @IsString()
  slLimitPrice?: string;

  @ApiPropertyOptional({
    description: 'Take profit order type',
    example: 'Market',
    enum: ['Market', 'Limit'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Market', 'Limit'])
  tpOrderType?: 'Market' | 'Limit';

  @ApiPropertyOptional({
    description: 'Stop loss order type',
    example: 'Market',
    enum: ['Market', 'Limit'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['Market', 'Limit'])
  slOrderType?: 'Market' | 'Limit';
}
