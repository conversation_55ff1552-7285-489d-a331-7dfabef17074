import { IsString, IsNotEmpty, IsIn } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for setting leverage on Bybit
 *
 * This DTO is used to validate and document the parameters required
 * for setting leverage on a specific symbol in Bybit trading.
 *
 * @example
 * {
 *   "category": "linear",
 *   "symbol": "BTCUSDT",
 *   "buyLeverage": "10",
 *   "sellLeverage": "10"
 * }
 */
export class SetLeverageDto {
  @ApiProperty({
    description: 'Product category',
    example: 'linear',
    enum: ['linear', 'inverse'],
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['linear', 'inverse'])
  category: 'linear' | 'inverse';

  @ApiProperty({
    description: 'Symbol name',
    example: 'BTCUSDT',
  })
  @IsString()
  @IsNotEmpty()
  symbol: string;

  @ApiProperty({
    description: 'Buy leverage',
    example: '10',
  })
  @IsString()
  @IsNotEmpty()
  buyLeverage: string;

  @ApiProperty({
    description: 'Sell leverage',
    example: '10',
  })
  @IsString()
  @IsNotEmpty()
  sellLeverage: string;
}
