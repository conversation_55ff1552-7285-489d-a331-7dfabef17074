import { <PERSON>tity, Column, PrimaryColumn, Index } from 'typeorm';

@Entity('historical')
@Index(['symbol', 'interval'])
export class HistoricalEntity {
  @Column({ nullable: false })
  symbol: string;

  @Column({ nullable: false })
  interval: string;

  @Column({ nullable: false })
  date: Date;

  @PrimaryColumn({ nullable: false })
  id: string;

  @Column({ nullable: false, type: 'float' })
  open: number;

  @Column({ nullable: false, type: 'float' })
  close: number;

  @Column({ nullable: false, type: 'float' })
  high: number;

  @Column({ nullable: false, type: 'float' })
  low: number;

  @Column({ nullable: true, type: 'float' })
  volume?: number;
}
