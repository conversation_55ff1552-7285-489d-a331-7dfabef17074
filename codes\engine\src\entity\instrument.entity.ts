import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  Index,
  OneToOne,
  JoinColumn,
} from 'typeorm';

@Entity('leverage-filter')
export class LeverageFilterEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'float' })
  minLeverage: number;

  @Column({ type: 'float' })
  maxLeverage: number;

  @Column({ type: 'float' })
  leverageStep: number;
}

@Entity('price-filter')
export class PriceFilterEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'float' })
  minPrice: number;

  @Column({ type: 'float' })
  maxPrice: number;

  @Column({ type: 'float' })
  tickSize: number;
}

@Entity('lot-size-filter')
export class LotSizeFilterEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'float' })
  maxOrderQty: number;

  @Column({ type: 'float' })
  minOrderQty: number;

  @Column({ type: 'float' })
  qtyStep: number;

  @Column({ type: 'float' })
  postOnlyMaxOrderQty: number;

  @Column({ type: 'float' })
  maxMktOrderQty: number;

  @Column({ type: 'float' })
  minNotionalValue: number;
}

@Entity('auction-fee-info')
export class AuctionFeeInfoEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  auctionFeeRate: string;

  @Column()
  takerFeeRate: string;

  @Column()
  makerFeeRate: string;
}

@Entity('instrument')
@Index(['symbol'])
export class InstrumentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  symbol: string;

  @Column({ type: 'bigint', nullable: true })
  launchTime: number;

  @Column({ type: 'bigint', nullable: true })
  listedTime: number;

  @Column({ type: 'int', nullable: true }) // ubah dari string
  priceScale: number;

  @OneToOne(() => LeverageFilterEntity, { cascade: true, eager: true })
  @JoinColumn()
  leverageFilter: LeverageFilterEntity;

  @OneToOne(() => PriceFilterEntity, { cascade: true, eager: true })
  @JoinColumn()
  priceFilter: PriceFilterEntity;

  @OneToOne(() => LotSizeFilterEntity, { cascade: true, eager: true })
  @JoinColumn()
  lotSizeFilter: LotSizeFilterEntity;

  @Column({ type: 'int', nullable: true })
  fundingInterval: number;

  @Column({ nullable: true })
  copyTrading: string;

  @Column({ type: 'float', nullable: true })
  upperFundingRate: number;

  @Column({ type: 'float', nullable: true })
  lowerFundingRate: number;

  @OneToOne(() => AuctionFeeInfoEntity, { cascade: true, eager: true })
  @JoinColumn()
  auctionFeeInfo: AuctionFeeInfoEntity;
}
