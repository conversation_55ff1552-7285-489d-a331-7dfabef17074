import { MethodType } from 'src/interface/method-type.interface';
import { Column, Entity, Index, PrimaryColumn } from 'typeorm';

@Entity('method-param')
@Index(['symbol', 'interval'])
export abstract class MethodParamEntity {
  @PrimaryColumn({ type: 'varchar', nullable: false })
  methodId: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 10 })
  interval: string;

  @Column({ type: 'timestamp' })
  start: Date;

  @Column({ type: 'timestamp' })
  end: Date;

  @Column({ type: 'bigint' })
  limit: number;

  @Column({ type: 'varchar', length: 10 })
  sort: 'ASC' | 'DESC';

  @Column({ type: 'varchar', length: 10 })
  trend: string;

  @Column({ type: 'varchar', length: 30 })
  patternType: string;

  @Column({ type: 'varchar', length: 50 })
  pattern: string;

  @Column({ type: 'varchar', length: 10 })
  orderType: string;

  @Column({ type: 'int' })
  validityPeriod: number;

  @Column({ type: 'float' })
  entryPercentByClose: number;

  @Column({ type: 'float' })
  riskPercent: number;

  @Column({ type: 'float' })
  rewardPercent: number;

  @Column({ type: 'int' })
  lookbackPeriod: number;

  @Column({ type: 'varchar', length: 10 })
  methodType: MethodType;

  @Column({ type: 'boolean' })
  enableOptimization: boolean;
}
