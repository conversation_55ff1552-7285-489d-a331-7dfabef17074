import { Column, Entity, Index, PrimaryColumn } from 'typeorm';

@Entity('method-performance')
@Index(['methodId', 'fromDate', 'endDate'], { unique: true })
export class MethodPerformanceEntity {
  @PrimaryColumn({ type: 'varchar', nullable: false })
  methodId: string;

  @Column({ type: 'timestamp' })
  fromDate: Date;

  @Column({ type: 'timestamp' })
  endDate: Date;

  @Column({ type: 'int' })
  totalValidTrade: number;

  @Column({ type: 'int' })
  totalInvalidTrade: number;

  @Column({ type: 'float' })
  averageRewardRiskRatio: number;

  @Column({ type: 'float' })
  probability: number;

  @Column({ type: 'int', nullable: true })
  avgOpenPosition: number;

  @Column({ type: 'float', nullable: true })
  avgStopPercent: number;

  @Column({ type: 'float', nullable: true })
  avgProfitPercent: number;

  @Column({ type: 'int', nullable: true })
  avgConsecutiveLoss: number;

  @Column({ type: 'int', nullable: true })
  avgConsecutiveProfit: number;

  @Column({ type: 'int', nullable: true })
  avgHoldingPeriod: number;

  @Column({ type: 'int' })
  maxOpenPosition: number;

  @Column({ type: 'float', nullable: true })
  maxStopPercent: number;

  @Column({ type: 'float', nullable: true })
  maxProfitPercent: number;

  @Column({ type: 'int' })
  maxConsecutiveLoss: number;

  @Column({ type: 'int' })
  maxConsecutiveProfit: number;

  @Column({ type: 'int' })
  maxHoldingPeriod: number;

  @Column({ type: 'float' })
  cumulativePercentage: number;

  @Column({ type: 'int', nullable: true })
  avgValidTradeByMonth: number;

  @Column({ type: 'int', nullable: true })
  avgValidTradeByDay: number;

  @Column({ type: 'float', nullable: true })
  avgRevenueByTrade: number;
}
