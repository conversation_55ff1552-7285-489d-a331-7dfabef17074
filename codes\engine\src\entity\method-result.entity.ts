import { Column, Entity, Index, PrimaryColumn } from 'typeorm';

@Entity('method-result')
@Index(['methodId', 'date'])
export class MethodResultEntity {
  @PrimaryColumn({ type: 'varchar' })
  id?: string;

  @Column({ type: 'varchar', length: 20 })
  interval: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 10 })
  orderType: string;

  @Column({ type: 'varchar', nullable: false })
  methodId: string;

  @Column({ type: 'timestamp' })
  date: Date;

  @Column({ type: 'float' })
  entry: number;

  @Column({ type: 'float' })
  stopLoss: number;

  @Column({ type: 'float' })
  takeProfit: number;

  @Column({ type: 'float' })
  stopPercent: number;

  @Column({ type: 'float' })
  profitPercent: number;

  @Column({ type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  openDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedDate?: Date;

  @Column({ type: 'varchar', length: 20 })
  status:
    | 'pending'
    | 'expired'
    | 'open'
    | 'profit'
    | 'loss'
    | 'invalid'
    | 'canceled';
}
