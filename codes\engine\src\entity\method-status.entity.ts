import { Entity, Column, PrimaryColumn, Index } from 'typeorm';

@Entity('method-status')
@Index(['methodId'])
export class MethodStatusEntity {
  @PrimaryColumn({ type: 'varchar', nullable: false })
  methodId: string;

  @Column({ type: 'varchar', length: 20 })
  symbol: string;

  @Column({ type: 'varchar', length: 10 })
  interval: string;

  @Column({ nullable: false })
  updatedAt: Date;

  @Column({ nullable: false, type: 'boolean' })
  isUpdated: boolean;
}
