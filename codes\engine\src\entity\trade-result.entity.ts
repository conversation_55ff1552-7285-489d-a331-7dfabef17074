import { OrderTriggerByV5, OrderTypeV5 } from 'bybit-api';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('trade-result')
@Index(['methodId', 'date'])
export class TradeResultEntity {
  @PrimaryColumn({ type: 'varchar' }) // Menggunakan @PrimaryColumn untuk custom ID
  id: string; // Akan diisi dengan result.id

  // OrderParamsV5 fields
  @Column({ type: 'varchar' })
  category: 'linear' | 'inverse' | 'spot' | 'option';

  @Column({ type: 'varchar' })
  symbol: string;

  @Column({ type: 'int', default: 1 })
  isLeverage: 0 | 1 | undefined;

  @Column({ type: 'varchar' })
  side: 'Buy' | 'Sell';

  @Column({ type: 'varchar' })
  orderType: 'Limit' | 'Market';

  @Column({ type: 'varchar' })
  qty: string;

  @Column({ type: 'varchar' })
  price: string;

  @Column({ type: 'int', nullable: true })
  triggerDirection: 1 | 2; // 1=Rising, 2=Falling

  @Column({ type: 'varchar', nullable: true })
  triggerPrice: string;

  @Column({ type: 'varchar', default: 'LastPrice' })
  triggerBy: OrderTriggerByV5 | undefined;

  @Column({ type: 'int' })
  positionIdx: 0 | 1 | 2; // 0=One-way, 1=Hedge Buy, 2=Hedge Sell

  @Column({ type: 'varchar', unique: true })
  orderLinkId: string;

  @Column({ type: 'varchar', nullable: true })
  orderId?: string;

  @Column({ type: 'varchar' })
  takeProfit: string;

  @Column({ type: 'varchar' })
  stopLoss: string;

  @Column({ type: 'varchar', default: 'LastPrice' })
  tpTriggerBy: OrderTriggerByV5 | undefined;

  @Column({ type: 'varchar', default: 'LastPrice' })
  slTriggerBy: OrderTriggerByV5 | undefined;

  @Column({ type: 'varchar', default: 'Partial' })
  tpslMode: 'Full' | 'Partial';

  @Column({ type: 'varchar', default: 'Market' })
  tpOrderType: OrderTypeV5 | undefined;

  @Column({ type: 'varchar', default: 'Market' })
  slOrderType: OrderTypeV5 | undefined;

  // TradeResult extended fields
  @Column({ type: 'varchar' })
  methodId: string;

  @Column({ type: 'timestamp' })
  date: Date;

  @Column({ type: 'varchar' })
  interval: string;

  @Column({ type: 'float' })
  stopPercent: number;

  @Column({ type: 'float' })
  profitPercent: number;

  @Column({ type: 'timestamp' })
  expiryDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  openDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedDate?: Date;

  @Column({
    type: 'varchar',
    default: 'pending',
  })
  status:
    | 'pending'
    | 'expired'
    | 'open'
    | 'profit'
    | 'loss'
    | 'invalid'
    | 'canceled';

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
