import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Request } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const apiKey = this.extractApiKeyFromRequest(request);

    if (!apiKey) {
      this.logger.warn(
        'API key missing from request',
        logDetail({
          class: 'ApiKeyGuard',
          function: 'canActivate',
          param: {
            userAgent: request.get('User-Agent'),
            path: request.path,
            ip: request.ip,
          },
        }),
      );
      return false;
    }

    const validApiKey = configurations('ADAPTER_EXTERNAL_API_KEY');
    if (!validApiKey) {
      this.logger.error(
        'ADAPTER_EXTERNAL_API_KEY not configured in environment',
        logDetail({
          class: 'ApiKeyGuard',
          function: 'canActivate',
        }),
      );
      return false;
    }

    if (apiKey !== validApiKey) {
      this.logger.warn(
        'Invalid API key provided',
        logDetail({
          class: 'ApiKeyGuard',
          function: 'canActivate',
          param: {
            userAgent: request.get('User-Agent'),
            path: request.path,
            ip: request.ip,
            providedApiKey: apiKey.substring(0, 8) + '***',
          },
        }),
      );
      return false;
    }

    this.logger.debug(
      'API key validation successful',
      logDetail({
        class: 'ApiKeyGuard',
        function: 'canActivate',
        param: { ip: request.ip, path: request.path },
      }),
    );

    return true;
  }

  private extractApiKeyFromRequest(request: Request): string | undefined {
    // Extract API key from Authorization header (Bearer token)
    const authHeader = request.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return undefined;
  }
}
