import * as fs from 'fs';
import * as path from 'path';

interface Historical {
  date: Date;
  symbol: string;
  interval: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

function convertToCsv(data: Historical[]): string {
  if (!data.length) return 'date,symbol,interval,open,high,low,close,volume\n';
  
  const header = 'date,symbol,interval,open,high,low,close,volume\n';
  const rows = data.map(item => 
    `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`
  ).join('\n');
  
  return header + rows;
}

function migrateJsonToCsv() {
  const dataDir = 'data/historical';
  
  if (!fs.existsSync(dataDir)) {
    console.log('No historical data directory found');
    return;
  }

  const symbolDirs = fs.readdirSync(dataDir);
  let migratedCount = 0;

  for (const symbolDir of symbolDirs) {
    const symbolPath = path.join(dataDir, symbolDir);
    if (!fs.statSync(symbolPath).isDirectory()) continue;

    const files = fs.readdirSync(symbolPath);
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const jsonPath = path.join(symbolPath, file);
        const csvPath = path.join(symbolPath, file.replace('.json', '.csv'));
        
        try {
          const jsonContent = fs.readFileSync(jsonPath, 'utf-8');
          const data = JSON.parse(jsonContent);
          
          if (Array.isArray(data)) {
            const csvContent = convertToCsv(data);
            fs.writeFileSync(csvPath, csvContent, 'utf-8');
            
            // Backup original JSON file
            const backupPath = `${jsonPath}.backup`;
            fs.renameSync(jsonPath, backupPath);
            
            console.log(`Migrated: ${jsonPath} -> ${csvPath}`);
            migratedCount++;
          }
        } catch (err) {
          console.error(`Failed to migrate ${jsonPath}:`, err);
        }
      }
    }
  }

  console.log(`Migration completed. ${migratedCount} files migrated.`);
}

// Run migration
migrateJsonToCsv();