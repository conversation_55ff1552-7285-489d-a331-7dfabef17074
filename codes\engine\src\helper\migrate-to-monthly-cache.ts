import * as fs from 'fs';
import * as path from 'path';

interface Historical {
  date: Date;
  symbol: string;
  interval: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export class MigrateToMonthlyCache {
  private getYearMonth(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${year}_${month}`;
  }

  private parseCsvFile(filePath: string): Historical[] {
    if (!fs.existsSync(filePath)) {
      return [];
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8').trim();
      if (!content) return [];

      const lines = content.split('\n');
      if (lines.length <= 1) return [];

      const validRecords: Historical[] = [];

      lines.slice(1).forEach((line) => {
        try {
          const [date, symbol, interval, open, high, low, close, volume] = line.split(',');

          if (!date || !symbol || !interval || !open || !high || !low || !close || !volume) {
            return;
          }

          const parsedDate = new Date(date);
          if (isNaN(parsedDate.getTime())) return;

          validRecords.push({
            date: parsedDate,
            symbol: symbol.trim(),
            interval: interval.trim(),
            open: parseFloat(open),
            high: parseFloat(high),
            low: parseFloat(low),
            close: parseFloat(close),
            volume: parseFloat(volume),
          });
        } catch (err) {
          // Skip invalid lines
        }
      });

      return validRecords;
    } catch (err) {
      console.error(`Failed to parse CSV file: ${filePath}`, err);
      return [];
    }
  }

  private convertToCsv(data: Historical[]): string {
    if (!data.length) return 'date,symbol,interval,open,high,low,close,volume\n';

    const header = 'date,symbol,interval,open,high,low,close,volume\n';
    const rows = data
      .map(
        (item) =>
          `${new Date(item.date).toISOString()},${item.symbol},${item.interval},${item.open},${item.high},${item.low},${item.close},${item.volume}`,
      )
      .join('\n');

    return header + rows;
  }

  async migrateSymbol(symbol: string): Promise<void> {
    const symbolPath = path.join('data/historical', symbol);
    
    if (!fs.existsSync(symbolPath)) {
      console.log(`Symbol directory not found: ${symbolPath}`);
      return;
    }

    const files = fs.readdirSync(symbolPath).filter(file => file.endsWith('.csv'));
    
    for (const file of files) {
      const interval = file.replace('.csv', '');
      const oldFilePath = path.join(symbolPath, file);
      
      console.log(`Migrating ${symbol}/${interval}...`);
      
      // Read existing data
      const data = this.parseCsvFile(oldFilePath);
      
      if (data.length === 0) {
        console.log(`No data found in ${oldFilePath}`);
        continue;
      }

      // Create new directory structure
      const newDirPath = path.join(symbolPath, interval);
      fs.mkdirSync(newDirPath, { recursive: true });

      // Group data by year_month
      const monthlyData = new Map<string, Historical[]>();
      data.forEach(item => {
        const yearMonth = this.getYearMonth(item.date);
        if (!monthlyData.has(yearMonth)) {
          monthlyData.set(yearMonth, []);
        }
        monthlyData.get(yearMonth)!.push(item);
      });

      // Write monthly files
      for (const [yearMonth, monthData] of monthlyData) {
        const monthFilePath = path.join(newDirPath, `${yearMonth}.csv`);
        const sortedData = monthData.sort((a, b) => a.date.getTime() - b.date.getTime());
        const csvContent = this.convertToCsv(sortedData);
        
        fs.writeFileSync(monthFilePath, csvContent, 'utf-8');
        console.log(`Created ${monthFilePath} with ${sortedData.length} records`);
      }

      // Backup old file
      const backupPath = `${oldFilePath}.backup.${Date.now()}`;
      fs.renameSync(oldFilePath, backupPath);
      console.log(`Backed up original file to ${backupPath}`);
    }
  }

  async migrateAll(): Promise<void> {
    const historicalPath = 'data/historical';
    
    if (!fs.existsSync(historicalPath)) {
      console.log('Historical data directory not found');
      return;
    }

    const symbols = fs.readdirSync(historicalPath).filter(item => {
      const itemPath = path.join(historicalPath, item);
      return fs.statSync(itemPath).isDirectory();
    });

    console.log(`Found ${symbols.length} symbols to migrate`);

    for (const symbol of symbols) {
      try {
        await this.migrateSymbol(symbol);
        console.log(`✓ Completed migration for ${symbol}`);
      } catch (err) {
        console.error(`✗ Failed to migrate ${symbol}:`, err);
      }
    }

    console.log('Migration completed!');
  }
}

// CLI usage
if (require.main === module) {
  const migrator = new MigrateToMonthlyCache();
  
  const args = process.argv.slice(2);
  if (args.length > 0) {
    // Migrate specific symbol
    migrator.migrateSymbol(args[0]).catch(console.error);
  } else {
    // Migrate all symbols
    migrator.migrateAll().catch(console.error);
  }
}