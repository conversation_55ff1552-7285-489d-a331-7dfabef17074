import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import cluster from 'cluster';
import os from 'os';
import configurations from './configurations';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import bodyParser from 'body-parser';
import { delay } from './util/delay.util';

async function startApp() {
  const startTime = Date.now();
  console.log(
    `[${new Date().toISOString()}] Starting application on platform: ${process.platform}, PID: ${process.pid}`,
  );

  const app = await NestFactory.create(AppModule);

  // Use Winston for logging
  const logger = app.get(WINSTON_MODULE_NEST_PROVIDER);
  app.useLogger(logger);

  logger.log(
    `Application created successfully on ${process.platform}`,
    'Bootstrap',
  );
  logger.log(`Process ID: ${process.pid}`, 'Bootstrap');
  logger.log(`Node.js version: ${process.version}`, 'Bootstrap');
  logger.log(`Working directory: ${process.cwd()}`, 'Bootstrap');

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  app.use(bodyParser.json({ limit: '10mb' }));
  app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  const swaggerConfig = new DocumentBuilder()
    .setTitle(configurations('APP_NAME'))
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'API Key',
      description: 'Enter your API key as Bearer token',
    })
    .build();

  if (configurations('NODE_ENV') === 'development') {
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('', app, document);
  }

  const port = configurations('PORT') || 3000;

  logger.log(`Starting server on port ${port}`, 'Bootstrap');
  logger.log(`Environment: ${configurations('NODE_ENV')}`, 'Bootstrap');
  logger.log(`Engine mode: ${configurations('ENGINE_MODE')}`, 'Bootstrap');
  logger.log(
    `Cluster disabled: ${configurations('DISABLE_CLUSTER')}`,
    'Bootstrap',
  );

  await app.listen(port);

  const bootDuration = Date.now() - startTime;
  logger.log(
    `Application started successfully in ${bootDuration}ms`,
    'Bootstrap',
  );
  console.log(
    `[${new Date().toISOString()}] Application is running on port ${port} (${process.platform}, PID: ${process.pid})`,
  );
}

async function startCluster() {
  const allocatedCore = configurations('CPU_ALLOCATION')
    ? Math.min(os.cpus().length, configurations('CPU_ALLOCATION'))
    : os.cpus().length;

  if (cluster.isPrimary) {
    console.log(
      `[${new Date().toISOString()}] Cluster primary started on ${process.platform}, PID: ${process.pid}`,
    );
    console.log(
      `[${new Date().toISOString()}] Total CPU cores: ${os.cpus().length}, Allocated: ${allocatedCore}`,
    );
    console.log(
      `[${new Date().toISOString()}] Starting ${allocatedCore} worker(s)...`,
    );

    for (let i = 0; i < allocatedCore; i++) {
      console.log(
        `[${new Date().toISOString()}] Forking worker ${i + 1}/${allocatedCore}`,
      );
      cluster.fork({
        CORE_NUMBER: i,
      });
    }

    cluster.on('online', (worker) => {
      console.log(
        `[${new Date().toISOString()}] Worker ${worker.process.pid} is online`,
      );
    });

    cluster.on('exit', (worker, code, signal) => {
      console.log(
        `[${new Date().toISOString()}] Worker ${worker.process.pid} exited with code ${code} and signal ${signal}`,
      );
      console.log(`[${new Date().toISOString()}] Creating a new worker...`);
      const CORE_NUMBER = process.env.CORE_NUMBER;
      cluster.fork({
        CORE_NUMBER,
      });
    });
  } else {
    let coreNumber = 0;
    const workerId = cluster.worker?.id || 0;
    for (let i = -(workerId + 1); i < allocatedCore; i += allocatedCore) {
      coreNumber = i;
    }
    console.log(
      `[${new Date().toISOString()}] Worker ${coreNumber} with PID ${process.pid} starting on ${process.platform}...`,
    );

    const delayMs = Math.round(1000 / allocatedCore);
    console.log(
      `[${new Date().toISOString()}] Worker ${coreNumber} waiting ${delayMs}ms before startup`,
    );
    await delay(delayMs);

    await startApp();
  }
}

console.log(
  `[${new Date().toISOString()}] Main process starting - Cluster disabled: ${configurations('DISABLE_CLUSTER')}`,
);

if (configurations('DISABLE_CLUSTER') === 'true') {
  console.log(`[${new Date().toISOString()}] Starting in single process mode`);
  startApp();
} else {
  console.log(`[${new Date().toISOString()}] Starting in cluster mode`);
  startCluster();
}
