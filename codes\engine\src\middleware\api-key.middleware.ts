import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';

@Injectable()
export class ApiKeyMiddleware implements NestMiddleware {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Skip API key validation for health check and other public endpoints
    const publicPaths = ['/health', '/'];
    if (publicPaths.some((path) => req.path.startsWith(path))) {
      return next();
    }

    const apiKey = this.extractApiKeyFromRequest(req);

    if (!apiKey) {
      this.logger.warn(
        'API key missing from request',
        logDetail({
          class: 'ApiKeyMiddleware',
          function: 'use',
          param: {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path,
          },
        }),
      );
      return res.status(401).json({ error: 'API key is required' });
    }

    const validApiKey = configurations('ADAPTER_EXTERNAL_API_KEY');
    if (!validApiKey) {
      this.logger.error(
        'ADAPTER_EXTERNAL_API_KEY not configured in environment',
        logDetail({
          class: 'ApiKeyMiddleware',
          function: 'use',
        }),
      );
      return res.status(401).json({ error: 'API validation not configured' });
    }

    if (apiKey !== validApiKey) {
      this.logger.warn(
        'Invalid API key provided',
        logDetail({
          class: 'ApiKeyMiddleware',
          function: 'use',
          param: {
            userAgent: req.get('User-Agent'),
            path: req.path,
            ip: req.ip,
            providedApiKey: apiKey.substring(0, 8) + '***',
          },
        }),
      );
      return res.status(401).json({ error: 'Invalid API key' });
    }

    this.logger.debug(
      'API key validation successful',
      logDetail({
        class: 'ApiKeyMiddleware',
        function: 'use',
        param: { ip: req.ip, path: req.path },
      }),
    );

    next();
  }

  private extractApiKeyFromRequest(request: Request): string | undefined {
    // Extract API key from Authorization header (Bearer token)
    const authHeader = request.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return undefined;
  }
}
