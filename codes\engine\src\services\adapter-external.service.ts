import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import axios, { AxiosError } from 'axios';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import {
  AmendOrderParamsV5,
  CancelOrderParamsV5,
  OrderParamsV5,
  SetLeverageParamsV5,
} from 'bybit-api';
import { Instrument } from 'src/interface/instrument.interface';
import { delay } from 'src/util/delay.util';

const ADAPTER_EXTERNAL_URL = configurations('ADAPTER_EXTERNAL_URL');
const ADAPTER_EXTERNAL_API_KEY = configurations('ADAPTER_EXTERNAL_API_KEY');

const axiosInstance = axios.create({
  baseURL: ADAPTER_EXTERNAL_URL,
  headers: {
    'x-api-key': ADAPTER_EXTERNAL_API_KEY,
  },
  timeout: 5000,
});

@Injectable()
export class AdapterExternalService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async switchToHedgeMode() {
    try {
      await axiosInstance.post('/switch-to-hedge-mode', {
        category: 'linear',
        mode: 3,
      });
    } catch (error) {
      this.logger.error(
        'Failed to switch to hedge mode',
        logDetail({
          class: 'AdapterExternalService',
          function: 'switchToHedgeMode',
          error,
        }),
      );
      await delay(5000);
      return await this.switchToHedgeMode();
    }
  }

  async setLeverage(param: SetLeverageParamsV5) {
    try {
      await axiosInstance.post('/set-leverage', param);
    } catch (error) {
      this.logger.error(
        'Failed to set leverage',
        logDetail({
          class: 'AdapterExternalService',
          function: 'setLeverage',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.setLeverage(param);
    }
  }

  async getWalletBalance(): Promise<number> {
    try {
      const response = await axiosInstance.get('/wallet-balance', {
        params: { accountType: 'UNIFIED', coin: 'USDT' },
      });
      const walletBalance = Number(
        response.data?.result?.list?.[0]?.totalMarginBalance ?? 0,
      );
      return walletBalance;
    } catch (error) {
      this.logger.error(
        'Failed to get wallet balance',
        logDetail({
          class: 'AdapterExternalService',
          function: 'getWalletBalance',
          error,
        }),
      );
      await delay(5000);
      return await this.getWalletBalance();
    }
  }

  async placeOrder(param: OrderParamsV5) {
    try {
      const response = await axiosInstance.post('/place-order', param);
      this.logger.info(
        JSON.stringify({
          function: 'placeOrder',
          param,
          response: response.data,
        }),
      );
    } catch (error) {
      this.logger.error(
        'Failed to place active order',
        logDetail({
          class: 'AdapterExternalService',
          function: 'placeOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.placeOrder(param);
    }
  }

  async amendOrder(param: AmendOrderParamsV5) {
    try {
      const response = await axiosInstance.post('/amend-order', param);
      this.logger.info(
        JSON.stringify({
          function: 'amendOrder',
          param,
          response: response.data,
        }),
      );
    } catch (error) {
      this.logger.error(
        'Failed to amend active order',
        logDetail({
          class: 'AdapterExternalService',
          function: 'amendOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.amendOrder(param);
    }
  }

  async cancelOrder(param: CancelOrderParamsV5) {
    try {
      const response = await axiosInstance.post('/cancel-order', param);
      this.logger.info(
        JSON.stringify({
          function: 'cancelOrder',
          param,
          response: response.data,
        }),
      );
    } catch (error) {
      this.logger.error(
        'Failed to cancel active order',
        logDetail({
          class: 'AdapterExternalService',
          function: 'cancelOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.cancelOrder(param);
    }
  }

  async getActiveOrders(param: OrderParamsV5) {
    try {
      const response = await axiosInstance.get('/active-orders', {
        params: param,
      });
      return response.data?.result?.list ?? [];
    } catch (error) {
      this.logger.error(
        'Failed to get active orders',
        logDetail({
          class: 'AdapterExternalService',
          function: 'getActiveOrders',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.getActiveOrders(param);
    }
  }

  async getOrderHistory(param: OrderParamsV5) {
    try {
      const response = await axiosInstance.get('/order-history', {
        params: param,
      });
      return response.data?.result?.list ?? [];
    } catch (error) {
      this.logger.error(
        'Failed to get order history',
        logDetail({
          class: 'AdapterExternalService',
          function: 'getOrderHistory',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.getOrderHistory(param);
    }
  }

  async fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    const startTimestamp = param.start.getTime();
    const endTimestamp = param.end.getTime();

    try {
      const response = await axiosInstance.get('/historical', {
        params: {
          category: 'linear',
          symbol: param.symbol,
          interval: param.interval,
          start: startTimestamp,
          end: endTimestamp,
          limit: param.limit,
        },
      });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Adapter External API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitHistorical',
            param,
          }),
        );
        return [];
      }

      const historicals: Historical[] = rawData
        .map((item: string[]) => ({
          symbol: param.symbol,
          date: new Date(Number(item[0])),
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          interval: param.interval,
        }))
        .sort((a, b) => {
          if (param.sort === 'DESC') {
            return b.date.getTime() - a.date.getTime();
          } else {
            return a.date.getTime() - b.date.getTime();
          }
        });

      return historicals;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch historical data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitHistorical',
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
      await delay(5000);
      return await this.fetchBybitHistorical(param);
    }
  }

  async fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]> {
    try {
      const response = await axiosInstance.get('/instruments-info', {
        params: {
          category: 'linear',
          ...(param?.symbol ? { symbol: param.symbol } : {}),
          limit: configurations('DEFAULT_LIMIT'),
        },
      });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Adapter External API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitInstrument',
            param,
          }),
        );
        return [];
      }
      const parsedData: Instrument[] = rawData.map((item: any) => ({
        symbol: String(item.symbol ?? ''),
        launchTime: item.launchTime === undefined ? 0 : Number(item.launchTime),
        listedTime: item.listedTime === undefined ? 0 : Number(item.listedTime),
        priceScale: item.priceScale === undefined ? 0 : Number(item.priceScale),
        leverageFilter: {
          minLeverage: Number(item.leverageFilter?.minLeverage ?? 0),
          maxLeverage: Number(item.leverageFilter?.maxLeverage ?? 0),
          leverageStep: Number(item.leverageFilter?.leverageStep ?? 0),
        },
        priceFilter: {
          minPrice: Number(item.priceFilter?.minPrice ?? 0),
          maxPrice: Number(item.priceFilter?.maxPrice ?? 0),
          tickSize: Number(item.priceFilter?.tickSize ?? 0),
        },
        lotSizeFilter: {
          maxOrderQty: Number(item.lotSizeFilter?.maxOrderQty ?? 0),
          minOrderQty: Number(item.lotSizeFilter?.minOrderQty ?? 0),
          qtyStep: Number(item.lotSizeFilter?.qtyStep ?? 0),
          postOnlyMaxOrderQty: Number(
            item.lotSizeFilter?.postOnlyMaxOrderQty ?? 0,
          ),
          maxMktOrderQty: Number(item.lotSizeFilter?.maxMktOrderQty ?? 0),
          minNotionalValue: Number(item.lotSizeFilter?.minNotionalValue ?? 0),
        },
        fundingInterval:
          item.fundingInterval === null || item.fundingInterval === undefined
            ? 0
            : Number(item.fundingInterval),
        upperFundingRate: Number(item.upperFundingRate ?? 0),
        lowerFundingRate: Number(item.lowerFundingRate ?? 0),
        auctionFeeInfo: {
          auctionFeeRate: configurations('BYBIT_AUCTION_FEE_RATE'),
          takerFeeRate: configurations('BYBIT_TAKER_FEE_RATE'),
          makerFeeRate: configurations('BYBIT_MAKER_FEE_RATE'),
        },
      }));

      return parsedData;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch instrument data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitInstrument',
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
      await delay(5000);
      return await this.fetchBybitInstrument(param);
    }
  }
}
