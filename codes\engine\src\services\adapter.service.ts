import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import axios, { AxiosError } from 'axios';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import {
  AmendOrderParamsV5,
  CancelOrderParamsV5,
  OrderParamsV5,
  RestClientV5,
  SetLeverageParamsV5,
  GetAccountOrdersParamsV5,
  GetAccountHistoricOrdersParamsV5,
  AccountOrderV5,
} from 'bybit-api';
import { Instrument } from 'src/interface/instrument.interface';
import { delay } from 'src/util/delay.util';
import { ExternalAdapterService } from './external-adapter.service';
import { TradeResult } from 'src/interface/trade-result.interface';

@Injectable()
export class AdapterService {
  private readonly isExternalEnabled: boolean;

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly externalAdapterService: ExternalAdapterService,
  ) {
    this.isExternalEnabled = configurations('ADAPTER_EXTERNAL_ENABLED');
  }

  async switchToHedgeMode(): Promise<any> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.switchToHedgeMode({});
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.switchPositionMode({
        category: 'linear',
        mode: 3,
      });
      return response;
    } catch (error) {
      this.logger.error(
        'Failed to switch to hedge mode',
        logDetail({
          class: 'AdapterService',
          function: 'switchToHedgeMode',
          error,
        }),
      );
      await delay(5000);
      return await this.switchToHedgeMode();
    }
  }

  async setLeverage(param: SetLeverageParamsV5): Promise<any> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.setLeverage(param);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.setLeverage(param);
      return response;
    } catch (error) {
      this.logger.error(
        'Failed to set leverage',
        logDetail({
          class: 'AdapterService',
          function: 'setLeverage',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.setLeverage(param);
    }
  }

  async getWalletBalance(): Promise<number> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.getWalletBalance();
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.getWalletBalance({
        accountType: 'UNIFIED',
        coin: 'USDT',
      });
      const walletBalance = Number(response.result.list[0].totalMarginBalance);
      return walletBalance;
    } catch (error) {
      this.logger.error(
        'Failed to get wallet balance',
        logDetail({
          class: 'AdapterService',
          function: 'getWalletBalance',
          error,
        }),
      );
      await delay(5000);
      return await this.getWalletBalance();
    }
  }

  async placeOrder(param: OrderParamsV5): Promise<any> {
    delete param.orderLinkId;
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.placeOrder(param);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.submitOrder(param);
      this.logger.info(
        JSON.stringify({ function: 'placeOrder', param, response }),
      );
      return response;
    } catch (error) {
      this.logger.error(
        'Failed to place active order',
        logDetail({
          class: 'AdapterService',
          function: 'placeOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.placeOrder(param);
    }
  }

  async amendOrder(param: TradeResult | AmendOrderParamsV5): Promise<any> {
    const order: AmendOrderParamsV5 = {
      category: param.category,
      symbol: param.symbol,
      orderId: param.orderId,
      qty: param.qty,
      price: param.price,
      takeProfit: param.takeProfit,
      stopLoss: param.stopLoss,
      tpTriggerBy: param.tpTriggerBy,
      slTriggerBy: param.slTriggerBy,
      tpLimitPrice: param.tpLimitPrice,
      slLimitPrice: param.slLimitPrice,
      triggerPrice: param.triggerPrice,
      triggerBy: param.triggerBy,
      
    };

    if (this.isExternalEnabled) {
      return await this.externalAdapterService.amendOrder(order);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.amendOrder(order);
      this.logger.info(
        JSON.stringify({ function: 'amendOrder', param, response }),
      );
      return response;
    } catch (error) {
      this.logger.error(
        'Failed to amend active order',
        logDetail({
          class: 'AdapterService',
          function: 'amendOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.amendOrder(param);
    }
  }

  async cancelOrder(param: TradeResult | CancelOrderParamsV5): Promise<any> {
    const order: CancelOrderParamsV5 = {
      category: param.category,
      symbol: param.symbol,
      orderId: param.orderId,
    };

    if (this.isExternalEnabled) {
      return await this.externalAdapterService.cancelOrder(order);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.cancelOrder(order);
      this.logger.info(
        JSON.stringify({ function: 'cancelOrder', param, response }),
      );
      return response;
    } catch (error) {
      this.logger.error(
        'Failed to cancel active order',
        logDetail({
          class: 'AdapterService',
          function: 'cancelOrder',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.cancelOrder(param);
    }
  }

  async getActiveOrders(
    param: GetAccountOrdersParamsV5,
  ): Promise<AccountOrderV5[]> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.getActiveOrders(param);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.getActiveOrders(param);
      return response.result.list;
    } catch (error) {
      this.logger.error(
        'Failed to get active orders',
        logDetail({
          class: 'AdapterService',
          function: 'getActiveOrders',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.getActiveOrders(param);
    }
  }

  async getOrderHistory(
    param: GetAccountHistoricOrdersParamsV5,
  ): Promise<AccountOrderV5[]> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.getOrderHistory(param);
    }
    const client = new RestClientV5({
      testnet: false,
      key: configurations('BYBIT_API_KEY'),
      secret: configurations('BYBIT_SECRET_KEY'),
    });

    try {
      const response = await client.getHistoricOrders(param);
      return response.result.list;
    } catch (error) {
      this.logger.error(
        'Failed to get order history',
        logDetail({
          class: 'AdapterService',
          function: 'getOrderHistory',
          error,
          param,
        }),
      );
      await delay(5000);
      return await this.getOrderHistory(param);
    }
  }

  async fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.getHistorical(param);
    }
    const startTimestamp = param.start.getTime();
    const endTimestamp = param.end.getTime();

    const url =
      `${configurations('BYBIT_API_HOST')}/v5/market/kline` +
      `?category=linear&symbol=${param.symbol}` +
      `&interval=${param.interval}&start=${startTimestamp}&end=${endTimestamp}&limit=${param.limit}`;

    try {
      const response = await axios.get(url, { timeout: 5000 });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Bybit API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitHistorical',
            url,
            param,
          }),
        );
        return [];
      }

      const historicals: Historical[] = rawData
        .map((item: string[]) => ({
          symbol: param.symbol,
          date: new Date(Number(item[0])),
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          interval: param.interval,
        }))
        .sort((a, b) => {
          if (param.sort === 'DESC') {
            return b.date.getTime() - a.date.getTime();
          } else {
            return a.date.getTime() - b.date.getTime();
          }
        });

      return historicals;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch historical data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitHistorical',
          url,
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
      await delay(5000);
      return await this.fetchBybitHistorical(param);
    }
  }

  async fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]> {
    if (this.isExternalEnabled) {
      return await this.externalAdapterService.getInstrument(param);
    }
    let url = '';
    if (!param) {
      url =
        `${configurations('BYBIT_API_HOST')}/v5/market/instruments-info` +
        `?category=linear` +
        `&limit=${configurations('DEFAULT_LIMIT')}`;
    } else {
      url =
        `${configurations('BYBIT_API_HOST')}/v5/market/instruments-info` +
        `?category=linear&symbol=${param.symbol}` +
        `&limit=${configurations('DEFAULT_LIMIT')}`;
    }

    try {
      const response = await axios.get(url, { timeout: 5000 });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Bybit API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitInstrument',
            url,
            param,
          }),
        );
        return [];
      }
      const parsedData: Instrument[] = rawData.map((item: any) => ({
        symbol: String(item.symbol ?? ''),

        launchTime: item.launchTime === undefined ? 0 : Number(item.launchTime),
        listedTime: item.listedTime === undefined ? 0 : Number(item.listedTime),

        priceScale: item.priceScale === undefined ? 0 : Number(item.priceScale),

        leverageFilter: {
          minLeverage: Number(item.leverageFilter?.minLeverage ?? 0),
          maxLeverage: Number(item.leverageFilter?.maxLeverage ?? 0),
          leverageStep: Number(item.leverageFilter?.leverageStep ?? 0),
        },

        priceFilter: {
          minPrice: Number(item.priceFilter?.minPrice ?? 0),
          maxPrice: Number(item.priceFilter?.maxPrice ?? 0),
          tickSize: Number(item.priceFilter?.tickSize ?? 0),
        },

        lotSizeFilter: {
          maxOrderQty: Number(item.lotSizeFilter?.maxOrderQty ?? 0),
          minOrderQty: Number(item.lotSizeFilter?.minOrderQty ?? 0),
          qtyStep: Number(item.lotSizeFilter?.qtyStep ?? 0),
          postOnlyMaxOrderQty: Number(
            item.lotSizeFilter?.postOnlyMaxOrderQty ?? 0,
          ),
          maxMktOrderQty: Number(item.lotSizeFilter?.maxMktOrderQty ?? 0),
          minNotionalValue: Number(item.lotSizeFilter?.minNotionalValue ?? 0),
        },

        fundingInterval:
          item.fundingInterval === null || item.fundingInterval === undefined
            ? 0
            : Number(item.fundingInterval),

        upperFundingRate: Number(item.upperFundingRate ?? 0),
        lowerFundingRate: Number(item.lowerFundingRate ?? 0),
        auctionFeeInfo: {
          auctionFeeRate: configurations('BYBIT_AUCTION_FEE_RATE'),
          takerFeeRate: configurations('BYBIT_TAKER_FEE_RATE'),
          makerFeeRate: configurations('BYBIT_MAKER_FEE_RATE'),
        },
      }));

      return parsedData;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch instrument data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitInstrument',
          url,
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
      await delay(5000);
      return await this.fetchBybitInstrument(param);
    }
  }
}
