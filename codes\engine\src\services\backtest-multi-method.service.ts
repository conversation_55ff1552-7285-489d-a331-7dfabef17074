import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { BacktestService } from './backtest.service';
import { logDetail } from 'src/util/log-detail.util';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';
import { MethodResult } from 'src/interface/method-result.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
import { MethodService } from './method.service';

@Injectable()
export class BacktestMultiMethodService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly backtestService: BacktestService,
    private readonly methodService: MethodService,
  ) {}

  async getBacktestMultiMethodResult(
    param: GetBacktestMultiMethodDto,
  ): Promise<MethodResult[]> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);

      return param.pendingResultOnly
        ? results.filter((item) => item.status === 'pending')
        : results;
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method result',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodResult',
          param,
          error,
        }),
      );
      return [];
    }
  }

  async getBacktestMultiMethodPerformance(
    param: GetBacktestMultiMethodDto,
  ): Promise<BacktestMultiPerformance> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);
      const totalMethod = [...new Set(results.map((item) => item.methodId))]
        .length;
      const performance = await this.backtestService.getPerformance(results);
      return {
        totalMethod,
        fromDate: performance.fromDate,
        endDate: performance.endDate,
        totalValidTrade: performance.totalValidTrade,
        totalInvalidTrade: performance.totalInvalidTrade,
        averageRewardRiskRatio: performance.averageRewardRiskRatio,
        probability: performance.probability,
        maxOpenPosition: performance.maxOpenPosition,
        maxStopPercent: performance.maxStopPercent,
        maxProfitPercent: performance.maxProfitPercent,
        maxConsecutiveLoss: performance.maxConsecutiveLoss,
        maxConsecutiveProfit: performance.maxConsecutiveProfit,
        maxHoldingPeriod: performance.maxHoldingPeriod,
        cumulativePercentage: performance.cumulativePercentage,
        avgRevenueByTrade: performance.avgRevenueByTrade,
        avgOpenPosition: performance.avgOpenPosition,
        avgStopPercent: performance.avgStopPercent,
        avgProfitPercent: performance.avgProfitPercent,
        avgConsecutiveLoss: performance.avgConsecutiveLoss,
        avgConsecutiveProfit: performance.avgConsecutiveProfit,
        avgHoldingPeriod: performance.avgHoldingPeriod,
        avgValidTradeByMonth: performance.avgValidTradeByMonth,
        avgValidTradeByDay: performance.avgValidTradeByDay,
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method performance',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodPerformance',
          param,
          error,
        }),
      );
      return {
        totalMethod: 0,
        fromDate: new Date(0),
        endDate: new Date(),
        totalValidTrade: 0,
        totalInvalidTrade: 0,
        averageRewardRiskRatio: 0,
        probability: 0,
        avgOpenPosition: 0,
        avgStopPercent: 0,
        avgProfitPercent: 0,
        avgConsecutiveLoss: 0,
        avgConsecutiveProfit: 0,
        avgHoldingPeriod: 0,
        maxOpenPosition: 0,
        maxStopPercent: 0,
        maxProfitPercent: 0,
        maxConsecutiveLoss: 0,
        maxConsecutiveProfit: 0,
        maxHoldingPeriod: 0,
        cumulativePercentage: 0,
        avgValidTradeByMonth: 0,
        avgValidTradeByDay: 0,
        avgRevenueByTrade: 0,
      };
    }
  }

  async getBacktestMutilMethodBoth(param: GetBacktestMultiMethodDto): Promise<{
    result: MethodResult[];
    performance: BacktestMultiPerformance;
  }> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);
      const totalMethod = [...new Set(results.map((item) => item.methodId))]
        .length;
      const performance = await this.backtestService.getPerformance(results);
      return {
        result: param.pendingResultOnly
          ? results.filter((item) => item.status === 'pending')
          : results,
        performance: {
          totalMethod,
          fromDate: performance.fromDate,
          endDate: performance.endDate,
          totalValidTrade: performance.totalValidTrade,
          totalInvalidTrade: performance.totalInvalidTrade,
          averageRewardRiskRatio: performance.averageRewardRiskRatio,
          probability: performance.probability,
          maxOpenPosition: performance.maxOpenPosition,
          maxStopPercent: performance.maxStopPercent,
          maxProfitPercent: performance.maxProfitPercent,
          maxConsecutiveLoss: performance.maxConsecutiveLoss,
          maxConsecutiveProfit: performance.maxConsecutiveProfit,
          maxHoldingPeriod: performance.maxHoldingPeriod,
          cumulativePercentage: performance.cumulativePercentage,
          avgRevenueByTrade: performance.avgRevenueByTrade,
          avgOpenPosition: performance.avgOpenPosition,
          avgStopPercent: performance.avgStopPercent,
          avgProfitPercent: performance.avgProfitPercent,
          avgConsecutiveLoss: performance.avgConsecutiveLoss,
          avgConsecutiveProfit: performance.avgConsecutiveProfit,
          avgHoldingPeriod: performance.avgHoldingPeriod,
          avgValidTradeByMonth: performance.avgValidTradeByMonth,
          avgValidTradeByDay: performance.avgValidTradeByDay,
        },
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method both',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodBoth',
          param,
          error,
        }),
      );
      return {
        result: [],
        performance: {
          totalMethod: 0,
          fromDate: new Date(0),
          endDate: new Date(),
          totalValidTrade: 0,
          totalInvalidTrade: 0,
          averageRewardRiskRatio: 0,
          probability: 0,
          avgOpenPosition: 0,
          avgStopPercent: 0,
          avgProfitPercent: 0,
          avgConsecutiveLoss: 0,
          avgConsecutiveProfit: 0,
          avgHoldingPeriod: 0,
          maxOpenPosition: 0,
          maxStopPercent: 0,
          maxProfitPercent: 0,
          maxConsecutiveLoss: 0,
          maxConsecutiveProfit: 0,
          maxHoldingPeriod: 0,
          cumulativePercentage: 0,
          avgValidTradeByMonth: 0,
          avgValidTradeByDay: 0,
          avgRevenueByTrade: 0,
        },
      };
    }
  }
}
