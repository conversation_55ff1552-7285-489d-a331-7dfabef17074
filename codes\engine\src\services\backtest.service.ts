import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import configurations from '../configurations';
import { logDetail } from 'src/util/log-detail.util';
import { MethodResult } from 'src/interface/method-result.interface';
import { calculateMaxConsecutive } from 'src/util/calculate-max-consecutive.util';
import { Plan } from 'src/interface/plan.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { generateResult } from 'src/util/generate-result.util';
import { calculateCummulativePercentage } from 'src/util/calculate-cummulative-percentage.util';
import { calculateRewardRiskRatio } from 'src/util/calculate-reward-risk-ratio.util';
import { Historical } from 'src/interface/historical.interface';
import { HistoricalCacheService } from './historical-cache.service';
import { calculateMaxStopProfitPercent } from 'src/util/calculate-max-stop-profit-percent.util';
import { calculateMaxOpenPosition } from 'src/util/calculate-max-open-position.util';
import { calculateMaxHoldingPeriod } from 'src/util/calculate-max-holding-period.util';
import { calculateAvgOpenPosition } from 'src/util/calculate-avg-open-position.util';
import { calculateAvgStopProfitPercent } from 'src/util/calculate-avg-stop-profit-percent.util';
import { calculateAvgConsecutive } from 'src/util/calculate-avg-consecutive.util';
import { calculateAvgHoldingPeriod } from 'src/util/calculate-avg-holding-period.utll';

@Injectable()
export class BacktestService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly historicalCacheService: HistoricalCacheService,
  ) {}

  async getMultiSymbolResult(param: Plan[]): Promise<MethodResult[]> {
    try {
      const symbols = [...new Set(param.map((item) => item.symbol))];
      const results: MethodResult[] = [];
      for (const symbol of symbols) {
        const historicalExecutionData =
          await this.historicalCacheService.getHistorical({
            symbol,
            interval: configurations('EXECUTION_INTERVAL'),
            start: new Date(param[0].date),
            end: new Date(),
            limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
            sort: 'ASC',
          });

        for (const item of param.filter((item) => item.symbol === symbol)) {
          const result = generateResult(item, historicalExecutionData);
          results.push(result);
        }
      }

      return results;
    } catch (error) {
      this.logger.error(
        'Failed to generate result',
        logDetail({
          class: 'AppService',
          function: 'generateResult',
          param,
          error,
        }),
      );
      return [];
    }
  }

  async getSingleSymbolResult(
    param: Plan[],
    historicalExecution?: Historical[],
  ): Promise<MethodResult[]> {
    try {
      if (!param.length) return [];
      const symbol = param[0].symbol;
      const results: MethodResult[] = [];

      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          symbol,
          interval: configurations('EXECUTION_INTERVAL'),
          start: new Date(param[0].date),
          end: new Date(),
          limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
          sort: 'ASC',
        }));

      for (const item of param.filter((item) => item.symbol === symbol)) {
        const result = generateResult(item, historicalExecutionData);
        results.push(result); // ✅ ID sudah di-set di generateResult
      }

      return results;
    } catch (error) {
      this.logger.error(
        'Failed to generate result',
        logDetail({
          class: 'AppService',
          function: 'generateResult',
          param,
          error,
        }),
      );
      return [];
    }
  }

  async getPerformance(param: MethodResult[]): Promise<BacktestPerformance> {
    const performance: BacktestPerformance = {
      methodId: '',
      fromDate: new Date(0),
      endDate: new Date(),
      totalValidTrade: 0,
      totalInvalidTrade: 0,
      probability: 0,
      avgOpenPosition: 0,
      avgStopPercent: 0,
      avgProfitPercent: 0,
      avgConsecutiveLoss: 0,
      avgConsecutiveProfit: 0,
      avgHoldingPeriod: 0,
      maxOpenPosition: 0,
      maxStopPercent: 0,
      maxProfitPercent: 0,
      maxConsecutiveLoss: 0,
      maxConsecutiveProfit: 0,
      cumulativePercentage: 0,
      averageRewardRiskRatio: 0,
      maxHoldingPeriod: 0,
      avgRevenueByTrade: 0,
      avgValidTradeByMonth: 0,
      avgValidTradeByDay: 0,
    };
    try {
      if (!param.length) return performance;

      const totalProfit = param.filter(
        (item) => item.status === 'profit',
      ).length;
      if (!totalProfit) return performance;

      const lossResult = param.filter((item) => item.status === 'loss');
      const totalLoss = lossResult.length;

      performance.methodId = param[0].methodId;
      performance.fromDate = param[0].date;
      performance.endDate = new Date();
      performance.totalValidTrade = totalProfit + totalLoss;
      performance.totalInvalidTrade = param.filter(
        (item) => item.status === 'invalid',
      ).length;
      performance.averageRewardRiskRatio = calculateRewardRiskRatio(param);
      performance.probability = Number(
        ((totalProfit / performance.totalValidTrade) * 100).toFixed(2),
      );
      performance.avgOpenPosition = calculateAvgOpenPosition(param);
      performance.avgStopPercent = calculateAvgStopProfitPercent(param, 'stop');
      performance.avgProfitPercent = calculateAvgStopProfitPercent(
        param,
        'profit',
      );
      performance.avgConsecutiveLoss = calculateAvgConsecutive(param, 'loss');
      performance.avgConsecutiveProfit = calculateAvgConsecutive(
        param,
        'profit',
      );
      performance.avgHoldingPeriod = calculateAvgHoldingPeriod(param);
      performance.maxOpenPosition = calculateMaxOpenPosition(param);
      performance.maxStopPercent = calculateMaxStopProfitPercent(param, 'stop');
      performance.maxProfitPercent = calculateMaxStopProfitPercent(
        param,
        'profit',
      );
      performance.maxConsecutiveLoss = calculateMaxConsecutive(param, 'loss');
      performance.maxConsecutiveProfit = calculateMaxConsecutive(
        param,
        'profit',
      );
      performance.maxHoldingPeriod = calculateMaxHoldingPeriod(param);
      performance.cumulativePercentage = calculateCummulativePercentage(param);
      performance.avgRevenueByTrade = Number(
        (
          performance.cumulativePercentage / performance.totalValidTrade
        ).toFixed(2),
      );

      performance.avgValidTradeByMonth = Math.ceil(
        performance.totalValidTrade /
          Math.ceil(
            (performance.endDate.getTime() - performance.fromDate.getTime()) /
              (1000 * 60 * 60 * 24 * 30),
          ),
      );
      performance.avgValidTradeByDay = Math.ceil(
        performance.avgValidTradeByMonth / 30,
      );

      return performance;
    } catch (error) {
      this.logger.error(
        'Failed to generate performance',
        logDetail({
          class: 'AppService',
          function: 'generatePerformance',
          param,
          error,
        }),
      );
      return performance;
    }
  }
}
