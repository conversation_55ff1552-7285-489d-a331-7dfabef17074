import { Injectable } from '@nestjs/common';
import { CandlestickService } from './candlestick.service';
import { Historical } from '../interface/historical.interface';

@Injectable()
export class CandlestickPatternService {
  constructor(private readonly candlestickService: CandlestickService) {}

  // BULLISH

  marubozu_white(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isBullish(candle) &&
      this.candlestickService.bodyRange(candle) /
        this.candlestickService.boneRange(candle) >
        0.95
    );
  }

  marubozu_black(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isBearish(candle) &&
      this.candlestickService.bodyRange(candle) /
        this.candlestickService.boneRange(candle) >
        0.95
    );
  }

  bullish_spinning_top(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyGreen(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.topDiv3bonePrice(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  bearishSpinningTop(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyRed(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.topDiv3bonePrice(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  grave_stone_doji(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isWithoutBody(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.topDiv3bonePrice(candle)
    );
  }

  dragon_fly_doji(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isWithoutBody(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  long_legged_doji(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isWithoutBody(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.topDiv3bonePrice(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  bullish_belt_hold(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isLongBodyGreen(candle) &&
      this.candlestickService.shadowTopRange(candle) >
        this.candlestickService.shadowBottomRange(candle)
    );
  }

  hammer(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyGreen(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.topDiv3bonePrice(candle)
    );
  }

  inverted_hammer(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 1;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyGreen(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  bullish_engulfing(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.bodyRange(secondCandle) >
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  piercing(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(firstCandle)
    );
  }

  bullish_harami(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  bullish_kicker(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle)
    );
  }

  bullish_meeting_line(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  matching_low(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      Math.abs(secondCandle.open - firstCandle.open) <=
        Math.max(
          this.candlestickService.bodyRange(secondCandle),
          this.candlestickService.bodyRange(firstCandle),
        ) *
          0.05
    );
  }

  tweezer_bottom(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      Math.abs(secondCandle.low - firstCandle.low) <=
        Math.max(
          this.candlestickService.boneRange(secondCandle),
          this.candlestickService.boneRange(firstCandle),
        ) *
          0.05
    );
  }

  bullish_separating_lines(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 2;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle)
    );
  }

  morning_star(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      (this.candlestickService.isBullish(secondCandle) ||
        this.candlestickService.isBearish(secondCandle)) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(thirdCandle) &&
      thirdCandle.close >= this.candlestickService.halfBodyPrice(firstCandle) &&
      firstCandle.open >= this.candlestickService.halfBodyPrice(thirdCandle)
    );
  }

  bullish_abandoned_baby(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isWithoutBody(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(thirdCandle) &&
      thirdCandle.close >= this.candlestickService.halfBodyPrice(firstCandle) &&
      firstCandle.open >= this.candlestickService.halfBodyPrice(thirdCandle)
    );
  }

  three_white_soldier(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) >=
        this.candlestickService.bottomBodyPrice(secondCandle)
    );
  }

  downside_gap_two_soldiers(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.topBodyPrice(thirdCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      // Gap
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  two_soldiers(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // Body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      thirdCandle.close < this.candlestickService.halfBodyPrice(firstCandle) &&
      // Gap
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  three_inside_up(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // standard
      this.candlestickService.bodyRange(firstCandle) >
        this.candlestickService.bodyRange(secondCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) <
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) >
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  three_outside_up(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Standard
      this.candlestickService.bodyRange(firstCandle) <
        this.candlestickService.bodyRange(secondCandle) &&
      this.candlestickService.topBodyPrice(firstCandle) >
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.topBodyPrice(firstCandle) <
        this.candlestickService.topBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) >
        this.candlestickService.bottomBodyPrice(secondCandle)
    );
  }

  three_stars_in_the_south(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // standard
      this.candlestickService.topBodyPrice(firstCandle) >
        this.candlestickService.topBodyPrice(secondCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(thirdCandle) &&
      this.candlestickService.bottomBodyPrice(firstCandle) <
        this.candlestickService.bottomBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(thirdCandle)
    );
  }

  bullish_stick_sandwich(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // standard
      Math.abs(firstCandle.low - thirdCandle.low) <=
        Math.max(
          this.candlestickService.boneRange(firstCandle),
          this.candlestickService.boneRange(thirdCandle),
        ) *
          0.05 &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      //gap
      !this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      !this.candlestickService.isGapBetweenCandles({
        firstCandle: thirdCandle,
        secondCandle,
      })
    );
  }

  upside_tasuki_gap(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // standard
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  bullish_side_by_side_white_lines(params: {
    candles: Historical[];
    trend: string;
  }) {
    const sampleCandleLength = 3;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Gap
      this.candlestickService.topBodyPrice(thirdCandle) >=
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) <=
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle: thirdCandle,
        secondCandle,
      })
    );
  }

  bullish_tri_star(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 4;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 4];
    const secondCandle = params.candles[params.candles.length - 3];
    const thirdCandle = params.candles[params.candles.length - 2];
    const fourthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isWithoutBody(secondCandle) &&
      this.candlestickService.isWithoutBody(thirdCandle) &&
      this.candlestickService.isWithoutBody(fourthCandle) &&
      // standard
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(thirdCandle)
    );
  }

  bullish_breakway(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 5;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 5];
    const secondCandle = params.candles[params.candles.length - 4];
    const thirdCandle = params.candles[params.candles.length - 3];
    const fourthCandle = params.candles[params.candles.length - 2];
    const fifthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      this.candlestickService.isBearish(fourthCandle) &&
      this.candlestickService.isBullish(fifthCandle) &&
      // standard
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle: thirdCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle: fourthCandle,
      }) &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(thirdCandle) <=
        this.candlestickService.topBodyPrice(secondCandle) &&
      this.candlestickService.topBodyPrice(fourthCandle) <=
        this.candlestickService.topBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(firstCandle)
    );
  }

  rising_three(params: { candles: Historical[]; trend: string }) {
    const sampleCandleLength = 5;
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: sampleCandleLength,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 5];
    const secondCandle = params.candles[params.candles.length - 4];
    const thirdCandle = params.candles[params.candles.length - 3];
    const fourthCandle = params.candles[params.candles.length - 2];
    const fifthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      this.candlestickService.isBearish(fourthCandle) &&
      this.candlestickService.isBullish(fifthCandle) &&
      // standard
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.topBodyPrice(firstCandle) <
        this.candlestickService.topBodyPrice(fifthCandle)
    );
  }

  // BEARISH

  bearish_belt_hold(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 1,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isLongBodyRed(candle) &&
      this.candlestickService.shadowBottomRange(candle) >
        this.candlestickService.shadowTopRange(candle)
    );
  }

  hanging_man(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 1,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyRed(candle) &&
      this.candlestickService.halfBodyPrice(candle) >
        this.candlestickService.topDiv3bonePrice(candle)
    );
  }

  shooting_star(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 1,
      })
    ) {
      return false;
    }
    const candle = params.candles[params.candles.length - 1];
    return (
      this.candlestickService.isShortBodyRed(candle) &&
      this.candlestickService.halfBodyPrice(candle) <
        this.candlestickService.bottomDiv3bonePrice(candle)
    );
  }

  bearish_engulfing(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      this.candlestickService.bodyRange(secondCandle) >
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  dark_cloud_cover(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(firstCandle)
    );
  }

  bearish_harami(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  bearish_kicker(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  bearish_meeting_line(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard

      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle)
    );
  }

  matching_high(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      // Standard
      Math.abs(secondCandle.open - firstCandle.open) <=
        Math.max(
          this.candlestickService.bodyRange(secondCandle),
          this.candlestickService.bodyRange(firstCandle),
        ) *
          0.05
    );
  }

  tweezer_top(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      Math.abs(secondCandle.low - firstCandle.low) <=
        Math.max(
          this.candlestickService.boneRange(secondCandle),
          this.candlestickService.boneRange(firstCandle),
        ) *
          0.05
    );
  }

  bearish_separating_lines(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 2,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 2];
    const secondCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.bottomBodyPrice(firstCandle)
    );
  }

  evening_star(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      (this.candlestickService.isBullish(secondCandle) ||
        this.candlestickService.isBearish(secondCandle)) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(thirdCandle) &&
      thirdCandle.close <= this.candlestickService.halfBodyPrice(firstCandle) &&
      firstCandle.open <= this.candlestickService.halfBodyPrice(thirdCandle)
    );
  }

  bearish_abandoned_baby(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isWithoutBody(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.halfBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(thirdCandle) &&
      thirdCandle.close <= this.candlestickService.halfBodyPrice(firstCandle) &&
      firstCandle.open <= this.candlestickService.halfBodyPrice(thirdCandle)
    );
  }

  three_black_crows(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(thirdCandle) <=
        this.candlestickService.topBodyPrice(secondCandle)
    );
  }

  upside_gap_two_crows(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <
        this.candlestickService.topBodyPrice(thirdCandle) &&
      // Gap
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  two_crows(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(thirdCandle) &&
      thirdCandle.close > this.candlestickService.halfBodyPrice(firstCandle) &&
      // Gap
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  three_inside_down(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // standard
      this.candlestickService.bodyRange(firstCandle) >
        this.candlestickService.bodyRange(secondCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.halfBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(thirdCandle) >
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(thirdCandle) <
        this.candlestickService.topBodyPrice(firstCandle)
    );
  }

  three_outside_down(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Standard
      this.candlestickService.bodyRange(firstCandle) <
        this.candlestickService.bodyRange(secondCandle) &&
      this.candlestickService.bottomBodyPrice(firstCandle) <
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(firstCandle) >
        this.candlestickService.bottomBodyPrice(secondCandle) &&
      this.candlestickService.topBodyPrice(thirdCandle) <
        this.candlestickService.topBodyPrice(secondCandle)
    );
  }

  advance_block(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // standard
      this.candlestickService.bottomBodyPrice(firstCandle) <
        this.candlestickService.bottomBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) <
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      this.candlestickService.topBodyPrice(firstCandle) >
        this.candlestickService.topBodyPrice(secondCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) >
        this.candlestickService.topBodyPrice(thirdCandle)
    );
  }

  bearish_stick_sandwich(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      // this.candlestickService.body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // standard
      Math.abs(firstCandle.high - thirdCandle.high) <=
        Math.max(
          this.candlestickService.boneRange(firstCandle),
          this.candlestickService.boneRange(thirdCandle),
        ) *
          0.05 &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.topBodyPrice(secondCandle) <=
        this.candlestickService.topBodyPrice(thirdCandle) &&
      //gap
      !this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      !this.candlestickService.isGapBetweenCandles({
        firstCandle: thirdCandle,
        secondCandle,
      })
    );
  }

  downside_tasuki_gap(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      // Gap
      this.candlestickService.isGapBetweenCandles({ firstCandle, secondCandle })
    );
  }

  bearish_side_by_side_white_lines(params: {
    candles: Historical[];
    trend: string;
  }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 3,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 3];
    const secondCandle = params.candles[params.candles.length - 2];
    const thirdCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBearish(secondCandle) &&
      this.candlestickService.isBearish(thirdCandle) &&
      // Gap
      this.candlestickService.topBodyPrice(thirdCandle) >=
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) <=
        this.candlestickService.halfBodyPrice(secondCandle) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle: thirdCandle,
        secondCandle,
      })
    );
  }

  bearish_tri_star(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 4,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 4];
    const secondCandle = params.candles[params.candles.length - 3];
    const thirdCandle = params.candles[params.candles.length - 2];
    const fourthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isWithoutBody(secondCandle) &&
      this.candlestickService.isWithoutBody(thirdCandle) &&
      this.candlestickService.isWithoutBody(fourthCandle) &&
      // standard
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.topBodyPrice(thirdCandle)
    );
  }

  bearish_breakway(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 5,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 5];
    const secondCandle = params.candles[params.candles.length - 4];
    const thirdCandle = params.candles[params.candles.length - 3];
    const fourthCandle = params.candles[params.candles.length - 2];
    const fifthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBullish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      this.candlestickService.isBullish(fourthCandle) &&
      this.candlestickService.isBearish(fifthCandle) &&
      // standard
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle: thirdCandle,
      }) &&
      this.candlestickService.isGapBetweenCandles({
        firstCandle,
        secondCandle: fourthCandle,
      }) &&
      this.candlestickService.bottomBodyPrice(secondCandle) >=
        this.candlestickService.bottomBodyPrice(firstCandle) &&
      this.candlestickService.bottomBodyPrice(thirdCandle) >=
        this.candlestickService.bottomBodyPrice(secondCandle) &&
      this.candlestickService.bottomBodyPrice(fourthCandle) >=
        this.candlestickService.bottomBodyPrice(thirdCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(firstCandle)
    );
  }

  failing_three(params: { candles: Historical[]; trend: string }) {
    if (
      !this.candlestickService.isTrendForCandlestickPattern({
        ...params,
        sampleCandleLength: 5,
      })
    ) {
      return false;
    }
    const firstCandle = params.candles[params.candles.length - 5];
    const secondCandle = params.candles[params.candles.length - 4];
    const thirdCandle = params.candles[params.candles.length - 3];
    const fourthCandle = params.candles[params.candles.length - 2];
    const fifthCandle = params.candles[params.candles.length - 1];
    return (
      //body
      this.candlestickService.isBearish(firstCandle) &&
      this.candlestickService.isBullish(secondCandle) &&
      this.candlestickService.isBullish(thirdCandle) &&
      this.candlestickService.isBullish(fourthCandle) &&
      this.candlestickService.isBearish(fifthCandle) &&
      // standard
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(firstCandle) &&
      this.candlestickService.bodyRange(secondCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.bodyRange(thirdCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.bodyRange(fourthCandle) <
        this.candlestickService.bodyRange(fifthCandle) &&
      this.candlestickService.bottomBodyPrice(firstCandle) >
        this.candlestickService.bottomBodyPrice(fifthCandle)
    );
  }
}
