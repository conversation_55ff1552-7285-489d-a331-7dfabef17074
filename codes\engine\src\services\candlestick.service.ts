import { Injectable } from '@nestjs/common';
import { Historical } from '../interface/historical.interface';
import configurations from '../configurations';

@Injectable()
export class CandlestickService {
  bodyRange(candle: Historical): number {
    return Math.abs(candle.open - candle.close);
  }

  topBodyPrice(candle: Historical): number {
    return Math.max(candle.open, candle.close);
  }

  bottomBodyPrice(candle: Historical): number {
    return Math.min(candle.open, candle.close);
  }

  halfBodyPrice(candle: Historical): number {
    return this.bodyRange(candle) / 2 + this.bottomBodyPrice(candle);
  }

  boneRange(candle: Historical): number {
    return Math.abs(candle.low - candle.high);
  }

  topBonePrice(candle: Historical): number {
    return candle.high;
  }

  bottomBonePrice(candle: Historical): number {
    return candle.low;
  }

  halfBonePrice(candle: Historical): number {
    return this.boneRange(candle) / 2 + this.bottomBonePrice(candle);
  }

  topDiv3bonePrice(candle: Historical): number {
    return (this.boneRange(candle) / 3) * 2 + this.bottomBonePrice(candle);
  }

  bottomDiv3bonePrice(candle: Historical): number {
    return (this.boneRange(candle) / 3) * 1 + this.bottomBonePrice(candle);
  }

  shadowTopRange(candle: Historical): number {
    return candle.close > candle.open
      ? Math.abs(candle.high - candle.close)
      : Math.abs(candle.high - candle.open);
  }

  shadowBottomRange(candle: Historical): number {
    return candle.close > candle.open
      ? Math.abs(candle.low - candle.open)
      : Math.abs(candle.low - candle.close);
  }

  isGapBetweenCandles(params: {
    firstCandle: Historical;
    secondCandle: Historical;
  }): boolean {
    return (
      this.topBodyPrice(params.secondCandle) <
        this.bottomBodyPrice(params.firstCandle) ||
      this.bottomBodyPrice(params.secondCandle) >
        this.topBodyPrice(params.firstCandle)
    );
  }

  isTrendForCandlestickPattern(params: {
    trend: string;
    candles: Historical[];
    sampleCandleLength: number;
  }): boolean {
    if (params.candles.length < configurations('PATTERN_TREND_SAMPLE_LENGTH')) {
      return false;
    }

    const trendSample = params.candles
      .slice(
        params.candles.length - configurations('PATTERN_TREND_SAMPLE_LENGTH'),
        params.candles.length,
      )
      .map((candle) => this.halfBodyPrice(candle));

    const movingAverage =
      trendSample.reduce(
        (accumulator, currentValue) => accumulator + currentValue,
        0,
      ) / trendSample.length;

    const trend =
      movingAverage < params.candles[params.candles.length - 1].low
        ? 'up'
        : movingAverage > params.candles[params.candles.length - 1].high
          ? 'down'
          : 'neutral';

    return params.trend === trend;
  }

  isBullish(candle: Historical): boolean {
    return candle.close > candle.open;
  }

  isBearish(candle: Historical): boolean {
    return candle.close < candle.open;
  }

  isLongBodyGreen(candle: Historical): boolean {
    const bodyRange = this.bodyRange(candle);
    const halfBoneRange = this.boneRange(candle) / 2;
    const boneRange = this.boneRange(candle);
    return (
      bodyRange >= halfBoneRange &&
      bodyRange < 0.95 * boneRange &&
      this.isBullish(candle)
    );
  }

  isShortBodyGreen(candle: Historical): boolean {
    const bodyRange = this.bodyRange(candle);
    const halfBoneRange = this.halfBonePrice(candle);
    const boneRange = this.boneRange(candle);

    return (
      bodyRange > 0 &&
      bodyRange < halfBoneRange &&
      bodyRange > 0.05 * boneRange &&
      this.isBullish(candle)
    );
  }

  isLongBodyRed(candle: Historical): boolean {
    const bodyRange = this.bodyRange(candle);
    const halfBoneRange = this.boneRange(candle) / 2;
    const boneRange = this.boneRange(candle);
    return (
      bodyRange >= halfBoneRange &&
      bodyRange < 0.95 * boneRange &&
      this.isBearish(candle)
    );
  }

  isShortBodyRed(candle: Historical): boolean {
    const bodyRange = this.bodyRange(candle);
    const halfBoneRange = this.halfBonePrice(candle);
    const boneRange = this.boneRange(candle);

    return (
      bodyRange > 0 &&
      bodyRange < halfBoneRange &&
      bodyRange > 0.05 * boneRange &&
      this.isBearish(candle)
    );
  }

  isWithoutBody(candle: Historical): boolean {
    const bodyRange = this.bodyRange(candle);
    const boneRange = this.boneRange(candle);

    return bodyRange <= 0.05 * boneRange;
  }
}
