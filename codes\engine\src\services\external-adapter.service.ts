import { Injectable, Inject } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import axios, { AxiosInstance } from 'axios';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';

/**
 * Service untuk mengirim request ke external adapter
 * Digunakan ketika ADAPTER_ENABLED=true
 */
@Injectable()
export class ExternalAdapterService {
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {
    this.baseUrl = configurations('ADAPTER_EXTERNAL_URL');
    this.apiKey = configurations('ADAPTER_EXTERNAL_API_KEY');

    if (!this.baseUrl) {
      this.logger.error(
        'ADAPTER_EXTERNAL_URL is required when ADAPTER_ENABLED is true',
        logDetail({
          class: 'ExternalAdapterService',
          function: 'constructor',
        }),
      );
      return;
    }

    if (!this.apiKey) {
      this.logger.error(
        'ADAPTER_EXTERNAL_API_KEY is required when ADAPTER_ENABLED is true',
        logDetail({
          class: 'ExternalAdapterService',
          function: 'constructor',
        }),
      );
      return;
    }

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.apiKey}`,
      },
    });

    // Add request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(
          'External adapter request',
          logDetail({
            class: 'ExternalAdapterService',
            function: 'request',
            url: config.url,
            param: { method: config.method?.toUpperCase(), data: config.data },
          }),
        );
        return config;
      },
      (error) => {
        this.logger.error(
          'External adapter request error',
          logDetail({
            class: 'ExternalAdapterService',
            function: 'request',
            error: error.message,
          }),
        );
        return Promise.reject(error);
      },
    );

    // Add response interceptor for logging
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(
          'External adapter response',
          logDetail({
            class: 'ExternalAdapterService',
            function: 'response',
            status: response.status,
            param: { url: response.config.url, data: response.data },
          }),
        );
        return response;
      },
      (error) => {
        this.logger.error(
          'External adapter response error',
          logDetail({
            class: 'ExternalAdapterService',
            function: 'response',
            status: error.response?.status,
            url: error.config?.url,
            error: error.response?.data || error.message,
          }),
        );
        return Promise.reject(error);
      },
    );
  }

  async switchToHedgeMode(param: any): Promise<any> {
    const response = await this.httpClient.post(
      '/adapter/switch-to-hedge-mode',
      param,
    );
    return response.data;
  }

  async setLeverage(param: any): Promise<any> {
    const response = await this.httpClient.post('/adapter/set-leverage', param);
    return response.data;
  }

  async getWalletBalance(): Promise<number> {
    const response = await this.httpClient.post('/adapter/get-wallet-balance');
    return response.data;
  }

  async placeOrder(param: any): Promise<any> {
    const response = await this.httpClient.post('/adapter/place-order', param);
    return response.data;
  }

  async amendOrder(param: any): Promise<any> {
    const response = await this.httpClient.post('/adapter/amend-order', param);
    return response.data;
  }

  async cancelOrder(param: any): Promise<any> {
    const response = await this.httpClient.post('/adapter/cancel-order', param);
    return response.data;
  }

  async getActiveOrders(param: any): Promise<any[]> {
    const response = await this.httpClient.post(
      '/adapter/active-orders',
      param,
    );
    return response.data;
  }

  async getOrderHistory(param: any): Promise<any[]> {
    const response = await this.httpClient.post(
      '/adapter/order-history',
      param,
    );
    return response.data;
  }

  async getHistorical(param: any): Promise<any[]> {
    const response = await this.httpClient.post('/adapter/historical', param);

    return response.data?.map((item: any) => ({
      ...item,
      date: new Date(item.date),
    }));
  }

  async getInstrument(param: any): Promise<any[]> {
    const response = await this.httpClient.post('/adapter/instrument', param);
    return response.data;
  }
}
