import { Inject, Injectable, Logger } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import configurations from '../configurations';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthCheckService {
  private static readonly TIMEOUT_MS = 3000;
  private readonly healthCheckUrls = [configurations('HOST_INSTRUMENT_API')];
  private isApplicationReady = false;

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly dataSource: DataSource,
  ) {
    // Mark as ready after initialization
    setTimeout(() => {
      this.isApplicationReady = true;
    }, 5000);
  }

  private async checkDatabase(): Promise<'reachable' | 'unreachable'> {
    try {
      if (!this.dataSource.isInitialized) {
        throw new Error('Database connection is not initialized');
      }
      await this.dataSource.query('SELECT 1');
      return 'reachable';
    } catch (err) {
      const error = err as Error;
      this.logger.error(
        `Database connection check failed: ${error.message}`,
        logDetail({
          class: 'HealthCheckService',
          function: 'checkDatabase',
          error,
        }),
      );
      return 'unreachable';
    }
  }

  private async checkUrl(url: string): Promise<'reachable' | 'unreachable'> {
    if (!url) return 'reachable'; // Skip if URL not configured
    
    try {
      const response = await axios.head(url, {
        timeout: HealthCheckService.TIMEOUT_MS,
      });
      if (response.status === 200) {
        return 'reachable';
      }
    } catch (err) {
      const error = err as AxiosError;
      this.logger.error(
        `Health check failed for ${url}: ${error.message}`,
        logDetail({
          class: 'HealthCheckService',
          function: 'checkUrl',
          url,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
    }
    return 'unreachable';
  }

  async checkStartup(): Promise<{
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    const timestamp = new Date().toISOString();
    const results: Record<string, 'reachable' | 'unreachable'> = {};
    
    // Only check database for startup
    results['database'] = await this.checkDatabase();
    results['application'] = this.isApplicationReady ? 'reachable' : 'unreachable';

    return { timestamp, results };
  }

  async checkReadiness(): Promise<{
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    const timestamp = new Date().toISOString();
    const results: Record<string, 'reachable' | 'unreachable'> = {};
    
    // Check all dependencies for readiness
    results['database'] = await this.checkDatabase();
    
    for (const url of this.healthCheckUrls) {
      if (url) {
        results[url] = await this.checkUrl(url);
      }
    }

    return { timestamp, results };
  }

  // Backward compatibility
  async check(): Promise<{
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    return this.checkReadiness();
  }
}
