import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { logDetail } from 'src/util/log-detail.util';
import configurations from 'src/configurations';
import { AdapterService } from './adapter.service';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { HistoricalCacheService } from './historical-cache.service';
import { HistoricalService } from './historical.service';
import { InstrumentService } from './instrument.service';
import { shuffleArray } from 'src/util/suffle-array.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import { MethodStatusService } from './method-status.service';

@Injectable()
export class HistoricalIngestionService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER)
    private readonly logger: Logger,
    private readonly historicalService: HistoricalService,
    private readonly methodStatusService: MethodStatusService,
    private readonly adapterService: AdapterService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly instrumentService: InstrumentService,
  ) {}

  async ingestHistorical(interval?: string) {
    const startTime = Date.now();
    const engineMode = configurations('ENGINE_MODE');
    const disableCluster = configurations('DISABLE_CLUSTER');
    const intervals = interval ? [interval] : configurations('INTERVALS');
    const executionInterval = configurations('EXECUTION_INTERVAL');

    this.logger.info(
      `Starting historical ingestion`,
      logDetail({
        class: 'HistoricalIngestionService',
        function: 'ingestHistorical',
        param: {
          engineMode,
          disableCluster,
          intervals,
          executionInterval,
          platform: process.platform,
          pid: process.pid,
          specificInterval: interval,
        },
      }),
    );

    // Worker mode
    if (engineMode === 'worker') {
      this.logger.info(
        `Running in worker mode`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestHistorical',
          param: { engineMode, disableCluster },
        }),
      );

      let symbols = await this.instrumentService.getSymbols();
      this.logger.info(
        `Retrieved ${symbols.length} symbols from instrument service`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestHistorical',
          param: { totalSymbols: symbols.length },
        }),
      );

      symbols =
        disableCluster === 'false'
          ? shuffleArray(getSymbolsSlice(symbols))
          : shuffleArray(symbols);

      this.logger.info(
        `Processed symbols for worker mode`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestHistorical',
          param: {
            finalSymbolCount: symbols.length,
            disableCluster,
            symbols: symbols.slice(0, 10), // Show first 10 symbols
          },
        }),
      );

      const intervalIds: string[] = [];
      for (const symbol of symbols) {
        for (const item of intervals) {
          intervalIds.push(`${symbol}-${item}`);
        }
      }

      this.logger.info(
        `Generated ${intervalIds.length} interval IDs for processing`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestHistorical',
          param: {
            totalIntervalIds: intervalIds.length,
            sampleIds: intervalIds.slice(0, 10), // Show first 10 IDs
          },
        }),
      );

      await this.ingestSymbols(symbols, [
        ...new Set(intervalIds.map((item) => item)),
      ]);

      const totalDuration = Date.now() - startTime;
      this.logger.info(
        `Completed historical ingestion in worker mode`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestHistorical',
          param: {
            totalDuration,
            symbolsProcessed: symbols.length,
            intervalIdsProcessed: intervalIds.length,
          },
        }),
      );
    }

    // Service mode
    if (engineMode === 'service') {
      const methodStatuses =
        await this.methodStatusService.getAllMethodStatus();

      let symbols = [...new Set(methodStatuses.map((item) => item.symbol))];

      symbols =
        disableCluster === 'false'
          ? shuffleArray(getSymbolsSlice(symbols))
          : shuffleArray(symbols);

      const intervalIds: string[] = [];
      for (const item of methodStatuses) {
        intervalIds.push(`${item.symbol}-${item.interval}`);
        intervalIds.push(`${item.symbol}-${executionInterval}`);
      }
      await this.ingestSymbols(symbols, [
        ...new Set(intervalIds.map((item) => item)),
      ]);
    }
  }

  private async ingestSymbols(symbols: string[], intervalIds: string[]) {
    const startTime = Date.now();

    this.logger.info(
      `Starting symbol ingestion`,
      logDetail({
        class: 'HistoricalIngestionService',
        function: 'ingestSymbols',
        param: {
          symbolCount: symbols.length,
          intervalIdCount: intervalIds.length,
          symbols: symbols.slice(0, 5), // Show first 5 symbols
        },
      }),
    );

    let processedSymbols = 0;
    for (const symbol of symbols) {
      const symbolStartTime = Date.now();
      const intervals = intervalIds
        .filter((item) => item.split('-')[0] === symbol)
        .map((item) => item.split('-')[1]);

      this.logger.info(
        `Processing symbol ${++processedSymbols}/${symbols.length}: ${symbol}`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestSymbols',
          param: {
            symbol,
            symbolIndex: processedSymbols,
            totalSymbols: symbols.length,
            intervals,
            intervalCount: intervals.length,
          },
        }),
      );

      let processedIntervals = 0;
      for (const interval of intervals) {
        const intervalStartTime = Date.now();

        this.logger.info(
          `Ingesting ${symbol} ${interval} (${++processedIntervals}/${intervals.length})`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'ingestSymbols',
            param: {
              symbol,
              interval,
              intervalIndex: processedIntervals,
              totalIntervals: intervals.length,
            },
          }),
        );

        try {
          await this.process(symbol, interval);

          const intervalDuration = Date.now() - intervalStartTime;
          this.logger.info(
            `Completed ingesting ${symbol} ${interval} in ${intervalDuration}ms`,
            logDetail({
              class: 'HistoricalIngestionService',
              function: 'ingestSymbols',
              param: {
                symbol,
                interval,
                duration: intervalDuration,
              },
            }),
          );
        } catch (error) {
          const intervalDuration = Date.now() - intervalStartTime;
          this.logger.error(
            `Failed to ingest ${symbol} ${interval} after ${intervalDuration}ms`,
            logDetail({
              class: 'HistoricalIngestionService',
              function: 'ingestSymbols',
              error,
              param: {
                symbol,
                interval,
                duration: intervalDuration,
              },
            }),
          );
          // Continue with next interval instead of stopping
        }
      }

      const symbolDuration = Date.now() - symbolStartTime;
      this.logger.info(
        `Completed processing symbol ${symbol} in ${symbolDuration}ms`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'ingestSymbols',
          param: {
            symbol,
            intervalsProcessed: intervals.length,
            duration: symbolDuration,
          },
        }),
      );
    }

    const totalDuration = Date.now() - startTime;
    this.logger.info(
      `Completed all symbol ingestion`,
      logDetail({
        class: 'HistoricalIngestionService',
        function: 'ingestSymbols',
        param: {
          totalSymbols: symbols.length,
          totalDuration,
          averageTimePerSymbol: Math.round(totalDuration / symbols.length),
        },
      }),
    );
  }

  private async process(symbol: string, interval: string) {
    const processStartTime = Date.now();

    this.logger.debug(
      `Starting process for ${symbol}-${interval}`,
      logDetail({
        class: 'HistoricalIngestionService',
        function: 'process',
        param: {
          symbol,
          interval,
          platform: process.platform,
          pid: process.pid,
        },
      }),
    );

    try {
      const startDate = new Date(0);
      const endDate = new Date();
      const defaultLimit = configurations('DEFAULT_LIMIT');
      const startTime = startDate.getTime();
      const endTime = endDate.getTime();
      const timeRange = toMiliseconds(interval);

      this.logger.debug(
        `Process configuration`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            defaultLimit,
            timeRange,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        }),
      );

      const instrument = await this.instrumentService.getInstrument({
        symbol,
      });

      if (!instrument) {
        this.logger.warn(
          `No instrument found for symbol: ${symbol}`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: { symbol, interval },
          }),
        );
        return;
      }

      this.logger.debug(
        `Instrument found for ${symbol}`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            listedTime: instrument[0].listedTime,
          },
        }),
      );

      const listedTime = new Date(Number(instrument[0].listedTime)).getTime();
      const rowLengthTarget = Math.ceil(
        (endTime - listedTime) / toMiliseconds(interval),
      );

      this.logger.debug(
        `Calculated processing parameters`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            listedTime: new Date(listedTime).toISOString(),
            rowLengthTarget,
            timeSpan: endTime - listedTime,
          },
        }),
      );

      let batchCount = 0;
      const totalBatches = Math.ceil(
        (endTime - startTime) / (timeRange * defaultLimit),
      );

      this.logger.debug(
        `Starting batch processing`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            totalBatches,
            batchSize: defaultLimit,
          },
        }),
      );

      for (let i = endTime; i > startTime; i -= timeRange * defaultLimit) {
        const batchStartTime = Date.now();
        batchCount++;

        const startScan = new Date(i - timeRange * defaultLimit);
        const endScan = new Date(i);

        this.logger.debug(
          `Processing batch ${batchCount}/${totalBatches} for ${symbol}-${interval}`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              totalBatches,
              startScan: startScan.toISOString(),
              endScan: endScan.toISOString(),
            },
          }),
        );

        // Step 1: Get full counts for cache and historical DB
        let existHistoricalCacheCount =
          await this.historicalCacheService.countHistorical(
            symbol,
            interval,
            startScan,
            endScan,
          );
        let existHistoricalCount = await this.historicalService.countHistorical(
          symbol,
          interval,
          startScan,
          endScan,
        );

        this.logger.debug(
          `Counts - Cache: ${existHistoricalCacheCount}, DB: ${existHistoricalCount}, Default Limit: ${defaultLimit}`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              existHistoricalCacheCount,
              existHistoricalCount,
              defaultLimit,
            },
          }),
        );

        // Step 2: Check if cache count is higher than DB count
        if (
          existHistoricalCacheCount === defaultLimit &&
          existHistoricalCount < defaultLimit
        ) {
          this.logger.info(
            `Cache has more data (${existHistoricalCacheCount}) than DB (${existHistoricalCount}). Syncing cache data for current batch to DB.`,
            logDetail({
              class: 'HistoricalIngestionService',
              function: 'process',
              param: {
                symbol,
                interval,
                cacheCount: existHistoricalCacheCount,
                dbCount: existHistoricalCount,
                batchIndex: batchCount,
                startScan: startScan.toISOString(),
                endScan: endScan.toISOString(),
              },
            }),
          );

          // Get cache data for current batch period
          const cacheData = await this.historicalCacheService.getHistorical({
            symbol,
            interval,
            sort: 'DESC',
            limit: defaultLimit,
            start: startScan,
            end: endScan,
          });

          if (cacheData.length === defaultLimit) {
            await this.historicalService.insertHistorical(cacheData);
            this.logger.info(
              `Successfully synced ${cacheData.length} records from cache to DB for batch ${batchCount}`,
              logDetail({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                  symbol,
                  interval,
                  batchIndex: batchCount,
                  recordsSynced: cacheData.length,
                  startScan: startScan.toISOString(),
                  endScan: endScan.toISOString(),
                },
              }),
            );
            existHistoricalCount = defaultLimit;
            continue;
          }
        }

        // Step 3: Check if DB count is higher than cache count
        if (
          existHistoricalCount === defaultLimit &&
          existHistoricalCacheCount < defaultLimit
        ) {
          this.logger.info(
            `DB has more data (${existHistoricalCount}) than cache (${existHistoricalCacheCount}). Syncing DB data for current batch to cache.`,
            logDetail({
              class: 'HistoricalIngestionService',
              function: 'process',
              param: {
                symbol,
                interval,
                dbCount: existHistoricalCount,
                cacheCount: existHistoricalCacheCount,
                batchIndex: batchCount,
                startScan: startScan.toISOString(),
                endScan: endScan.toISOString(),
              },
            }),
          );

          // Get DB data for current batch period
          const dbData = await this.historicalService.getHistorical({
            symbol,
            interval,
            sort: 'DESC',
            limit: defaultLimit,
            start: startScan,
            end: endScan,
          });

          if (dbData.length === defaultLimit) {
            await this.historicalCacheService.insertHistorical(dbData);
            this.logger.info(
              `Successfully synced ${dbData.length} records from DB to cache for batch ${batchCount}`,
              logDetail({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                  symbol,
                  interval,
                  batchIndex: batchCount,
                  recordsSynced: dbData.length,
                  startScan: startScan.toISOString(),
                  endScan: endScan.toISOString(),
                },
              }),
            );
            existHistoricalCacheCount = defaultLimit;
            continue;
          }
        }

        // Step 4: Both counts are equal
        if (
          existHistoricalCount === defaultLimit &&
          existHistoricalCacheCount === defaultLimit
        ) {
          const existHistoricalCacheFullCount =
            await this.historicalCacheService.countHistorical(symbol, interval);
          const existHistoricalFullCount =
            await this.historicalService.countHistorical(symbol, interval);
          if (
            existHistoricalCacheFullCount >= rowLengthTarget &&
            existHistoricalFullCount >= rowLengthTarget
          ) {
            this.logger.info(
              `Both cache and DB have reached target count. Breaking batch loop.`,
              logDetail({
                class: 'HistoricalIngestionService',
                function: 'process',
                param: {
                  symbol,
                  interval,
                  batchIndex: batchCount,
                  cacheCount: existHistoricalCacheFullCount,
                  dbCount: existHistoricalFullCount,
                  targetCount: rowLengthTarget,
                },
              }),
            );
            break;
          } else {
            continue;
          }
        }
        // Step 5: Fetch new data from Bybit (when both counts are equal but less than target)
        this.logger.debug(
          `Fetching new data for batch ${batchCount}`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              startScan: startScan.toISOString(),
              endScan: endScan.toISOString(),
              limit: defaultLimit,
            },
          }),
        );

        const fetchStartTime = Date.now();
        const newHistorical = await this.adapterService.fetchBybitHistorical({
          symbol,
          interval,
          sort: 'DESC',
          limit: defaultLimit,
          start: startScan,
          end: endScan,
        });

        const fetchDuration = Date.now() - fetchStartTime;
        this.logger.debug(
          `Fetched ${newHistorical.length} new records in ${fetchDuration}ms`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              recordsFetched: newHistorical.length,
              fetchDuration,
            },
          }),
        );

        const dbInsertStartTime = Date.now();
        await this.historicalService.insertHistorical(newHistorical);
        const dbInsertDuration = Date.now() - dbInsertStartTime;

        this.logger.debug(
          `Inserted ${newHistorical.length} records to DB in ${dbInsertDuration}ms`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              recordsInserted: newHistorical.length,
              dbInsertDuration,
            },
          }),
        );

        const cacheInsertStartTime = Date.now();
        await this.historicalCacheService.insertHistorical(newHistorical);
        const cacheInsertDuration = Date.now() - cacheInsertStartTime;

        this.logger.debug(
          `Inserted ${newHistorical.length} records to cache in ${cacheInsertDuration}ms`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              recordsInserted: newHistorical.length,
              cacheInsertDuration,
            },
          }),
        );

        // If no new data available, break the loop
        if (newHistorical.length < defaultLimit) {
          this.logger.info(
            `No more data available from Bybit for ${symbol}-${interval}`,
            logDetail({
              class: 'HistoricalIngestionService',
              function: 'process',
              param: {
                symbol,
                interval,
                batchIndex: batchCount,
                finalCount: existHistoricalCacheCount,
              },
            }),
          );
          break;
        }

        const batchDuration = Date.now() - batchStartTime;
        this.logger.debug(
          `Completed batch ${batchCount}/${totalBatches} in ${batchDuration}ms`,
          logDetail({
            class: 'HistoricalIngestionService',
            function: 'process',
            param: {
              symbol,
              interval,
              batchIndex: batchCount,
              totalBatches,
              batchDuration,
              recordsProcessed: newHistorical.length,
            },
          }),
        );
      }

      const totalProcessDuration = Date.now() - processStartTime;
      this.logger.info(
        `Completed processing ${symbol}-${interval} in ${totalProcessDuration}ms`,
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            totalBatches: batchCount,
            totalDuration: totalProcessDuration,
            averageBatchTime: Math.round(totalProcessDuration / batchCount),
          },
        }),
      );

      return;
    } catch (error) {
      const errorDuration = Date.now() - processStartTime;
      this.logger.error(
        'Failed to process historical data',
        logDetail({
          class: 'HistoricalIngestionService',
          function: 'process',
          param: {
            symbol,
            interval,
            duration: errorDuration,
            platform: process.platform,
          },
          error,
        }),
      );
      return;
    }
  }
}
