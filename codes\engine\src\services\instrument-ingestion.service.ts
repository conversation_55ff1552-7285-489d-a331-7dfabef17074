import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { AdapterService } from './adapter.service';
import { InstrumentService } from './instrument.service';
import { Instrument } from 'src/interface/instrument.interface';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { delay } from 'src/util/delay.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import configurations from 'src/configurations';
import { MethodStatusService } from './method-status.service';

@Injectable()
export class InstrumentIngestionService {
  constructor(
    private readonly adapterService: AdapterService,
    private readonly instrumentService: InstrumentService,
    private readonly methodStatusService: MethodStatusService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async ingest(enableListedTime?: boolean) {
    try {
      const disableCluster = configurations('DISABLE_CLUSTER');
      const engineMode = configurations('ENGINE_MODE');

      // Optimize symbol filtering by creating a Set for O(1) lookup
      const symbolsArray = await this.getSymbols(engineMode, disableCluster);
      const symbolsSet = new Set(symbolsArray);
      const newData = await this.adapterService.fetchBybitInstrument();
      const filteredData = newData.filter((item) =>
        symbolsSet.has(item.symbol),
      );
      const completedData = enableListedTime
        ? await this.ingestListedTime(filteredData)
        : filteredData;
      await this.instrumentService.insert(completedData, enableListedTime);
      return;
    } catch (error) {
      this.logger.error(
        'Ingestion process failed',
        logDetail({
          class: 'InstrumentIngestionService',
          function: 'ingest',
          error,
        }),
      );
      return;
    }
  }

  private async getSymbols(
    engineMode: string,
    disableCluster: string,
  ): Promise<string[]> {
    let symbols: string[];
    if (engineMode === 'worker') {
      symbols = [
        ...new Set(
          (await this.adapterService.fetchBybitInstrument()).map(
            (item) => item.symbol,
          ),
        ),
      ];
    } else {
      const statuses = await this.methodStatusService.getAllMethodStatus();
      symbols = [...new Set(statuses.map((item) => item.symbol))];
    }
    return disableCluster === 'false' ? getSymbolsSlice(symbols) : symbols;
  }

  private async ingestListedTime(newData: Instrument[]): Promise<Instrument[]> {
    const results: Instrument[] = [];
    const cpuAllocation =
      configurations('CPU_ALLOCATION') ?? configurations('TOTAL_CORE');
    const defaultLimit = configurations('DEFAULT_LIMIT');
    const oneDayMs = toMiliseconds('D');

    for (const data of newData) {
      const now = Date.now();
      for (let t = now; t > 0; t -= oneDayMs * defaultLimit) {
        const start = new Date(t - oneDayMs * defaultLimit);
        const end = new Date(t);

        const historical = await this.adapterService.fetchBybitHistorical({
          symbol: data.symbol,
          interval: 'D',
          start,
          end,
          limit: defaultLimit,
          sort: 'ASC',
        });

        if (historical.length) {
          data.listedTime = new Date(historical[0].date).getTime() + oneDayMs;
          await delay((1000 / 50) * cpuAllocation);
        } else {
          results.push(data);
          break;
        }
      }
    }
    return results;
  }
}
