import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import configurations from 'src/configurations';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { Instrument } from 'src/interface/instrument.interface';
import { delay } from 'src/util/delay.util';
import { logDetail } from 'src/util/log-detail.util';
import { In, Repository } from 'typeorm';
import { Logger } from 'winston';

@Injectable()
export class InstrumentService {
  constructor(
    @InjectRepository(InstrumentEntity)
    private readonly InstrumentsRepository: Repository<InstrumentEntity>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async getMinOfMaxLeverage(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
        .select('MIN(leverageFilter.maxLeverage)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.min ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMinOfMaxLeverage',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMinOfMaxLeverage();
    }
  }

  async getAvgOfMaxLeverage(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
        .select('AVG(leverageFilter.maxLeverage)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.avg ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getAvgOfMaxLeverage',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getAvgOfMaxLeverage();
    }
  }

  async getMaxLeverage(symbol: string): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
        .where('instrument.symbol = :symbol', { symbol })
        .select('leverageFilter.maxLeverage');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.leverageFilter_maxLeverage ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMaxLeverage',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMaxLeverage(symbol);
    }
  }

  async getMaxFundingRate(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      ).select('MAX(instrument.upperFundingRate)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.max ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMaxFundingRate',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMaxFundingRate();
    }
  }

  async getAvgFundingRate(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      ).select('AVG(instrument.upperFundingRate)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.avg ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getAvgFundingRate',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getAvgFundingRate();
    }
  }

  async getMinFundingInterval(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      ).select('MIN(instrument.fundingInterval)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.min ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMaxFundingInterval',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMinFundingInterval();
    }
  }

  async getAvgFundingInterval(): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      ).select('AVG(instrument.fundingInterval)');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.avg ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getAvgFundingInterval',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getAvgFundingInterval();
    }
  }

  async getMinOrderQty(symbol: string): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
        .where('instrument.symbol = :symbol', { symbol })
        .select('lotSizeFilter.minOrderQty');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.lotSizeFilter_minOrderQty ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMinOrderQty',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMinOrderQty(symbol);
    }
  }

  async getMaxOrderQty(symbol: string): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
        .where('instrument.symbol = :symbol', { symbol })
        .select('lotSizeFilter.maxOrderQty');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.lotSizeFilter_maxOrderQty ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getMaxOrderQty',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getMaxOrderQty(symbol);
    }
  }

  async getQtyStep(symbol: string): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter')
        .where('instrument.symbol = :symbol', { symbol })
        .select('lotSizeFilter.qtyStep');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.lotSizeFilter_qtyStep ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getQtyStep',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getQtyStep(symbol);
    }
  }

  async getInstrument(param: GetInstrumentDto): Promise<InstrumentEntity[]> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
        .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
        .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter');

      // Add conditions based on provided parameters
      if (param.symbol) {
        queryBuilder.andWhere('instrument.symbol = :symbol', {
          symbol: param.symbol,
        });
      }
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'read',
          error: err,
          param,
        }),
      );
      await delay(5000);
      return await this.getInstrument(param);
    }
  }

  async insert(
    param: Instrument[],
    enableListedTime?: boolean,
  ): Promise<InstrumentEntity[]> {
    try {
      const symbols = param.map((p) => p.symbol);
      const existingRecords = await this.InstrumentsRepository.find({
        where: { symbol: In(symbols) },
      });
      const existingMap = new Map(existingRecords.map((r) => [r.symbol, r]));
      const mergedRecords = param.map((record) => {
        const existing = existingMap.get(record.symbol);
        if (existing) {
          return {
            ...existing,
            ...record,
            listedTime: enableListedTime
              ? record.listedTime
              : existing.listedTime,
            auctionFeeInfo: {
              ...(existing.auctionFeeInfo ?? {}),
              ...(record.auctionFeeInfo ?? {}),
            },
            leverageFilter: {
              ...(existing.leverageFilter ?? {}),
              ...(record.leverageFilter ?? {}),
            },
            priceFilter: {
              ...(existing.priceFilter ?? {}),
              ...(record.priceFilter ?? {}),
            },
            lotSizeFilter: {
              ...(existing.lotSizeFilter ?? {}),
              ...(record.lotSizeFilter ?? {}),
            },
          };
        } else {
          return record;
        }
      });

      return await this.InstrumentsRepository.save(mergedRecords);
    } catch (err) {
      this.logger.error(
        'Failed to upsert instrument data',
        logDetail({
          class: 'AppService',
          function: 'insert',
          error: err,
          param,
        }),
      );
      await delay(5000);
      return await this.insert(param, enableListedTime);
    }
  }

  async getTickSize(symbol: string): Promise<number> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
        .where('instrument.symbol = :symbol', { symbol })
        .select('priceFilter.tickSize');
      const instrument = await queryBuilder.getRawOne();
      return instrument?.priceFilter_tickSize ?? 0;
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getTickSize',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getTickSize(symbol);
    }
  }

  async getSymbols(): Promise<string[]> {
    try {
      const launchTimeThreshold =
        new Date().getTime() -
        configurations('SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS') *
          24 *
          60 *
          60 *
          1000;
      const queryBuilder =
        this.InstrumentsRepository.createQueryBuilder('instrument');
      queryBuilder.andWhere('instrument.launchTime < :launchTimeThreshold', {
        launchTimeThreshold,
      });
      const symbols = await queryBuilder.select('instrument.symbol').getMany();
      return symbols.map((s) => s.symbol);
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getSymbols',
          error: err,
        }),
      );
      await delay(5000);
      return await this.getSymbols();
    }
  }
}
