import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { MethodService } from './method.service';
import { MethodStatusService } from './method-status.service';
import { BacktestMethodService } from './backtest-method.service';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { isGoodMethod } from 'src/util/is-good-method.util';
import configurations from 'src/configurations';

import { HistoricalCacheService } from './historical-cache.service';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { InstrumentService } from './instrument.service';

@Injectable()
export class MethodUpdaterService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly methodStatusService: MethodStatusService,
    private readonly methodService: MethodService,
    private readonly backtestMethodService: BacktestMethodService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly instrumentService: InstrumentService,
  ) {}

  async updateMethod(end: Date): Promise<void> {
    try {
      await this.syncHelper();
      for (let index = 0; index < 2; index++) {
        const methodStatuses = await this.methodStatusService.getMethodStatus();
        if (!methodStatuses || methodStatuses.length === 0) break;
        else index = 0;

        const intervals = [
          ...new Set(methodStatuses.map((item) => item.interval)),
        ];
        const symbols = [...new Set(methodStatuses.map((item) => item.symbol))];

        for (const symbol of symbols) {
          const instrument = await this.instrumentService.getInstrument({
            symbol,
          });
          if (!instrument) return;
          const listedTime = new Date(
            Number(instrument[0].listedTime),
          ).getTime();

          const historicalExecutionLengthTarget = Math.ceil(
            (end.getTime() - listedTime) /
              toMiliseconds(configurations('EXECUTION_INTERVAL')),
          );

          const historicalExecutionLength =
            await this.historicalCacheService.countHistorical(
              symbol,
              configurations('EXECUTION_INTERVAL'),
            );
          if (historicalExecutionLength < historicalExecutionLengthTarget)
            continue;

          const historicalExecutionData =
            await this.historicalCacheService.getHistorical({
              symbol,
              interval: configurations('EXECUTION_INTERVAL'),
              start: new Date(0),
              end,
              limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
              sort: 'ASC',
            });
          for (const interval of intervals) {
            for (const methodStatus of methodStatuses.filter(
              (item) => item.symbol === symbol && item.interval === interval,
            )) {
              const param = await this.methodService.getParam(methodStatus);

              if (!param) {
                await this.methodStatusService.deleteMethodStatus(
                  methodStatus.methodId,
                );
                await this.methodService.deleteMethodResult(
                  methodStatus.methodId,
                );
                await this.methodService.deleteMethodPerformance(
                  methodStatus.methodId,
                );
                await this.methodStatusService.deleteMethodStatus(
                  methodStatus.methodId,
                );
                continue;
              }

              const historicalLengthTarget = Math.ceil(
                (end.getTime() - listedTime) / toMiliseconds(interval),
              );

              const historicalLength =
                await this.historicalCacheService.countHistorical(
                  symbol,
                  interval,
                );
              if (historicalLength < historicalLengthTarget) continue;

              const historicalData =
                await this.historicalCacheService.getHistorical({
                  symbol,
                  interval,
                  start: new Date(0),
                  end,
                  limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
                  sort: 'ASC',
                });

              const methodParam: GetBacktestMethodDto = param[0];

              const { result, performance } =
                await this.backtestMethodService.getBacktestMethodBoth(
                  methodParam,
                  historicalData,
                  historicalExecutionData,
                );

              if (isGoodMethod(result, performance)) {
                await this.methodService.insertMethodParam(methodParam);
                await this.methodService.insertMethodResult(result);
                await this.methodService.insertMethodPerformance(performance);
                await this.methodStatusService.updateMethodStatus({
                  symbol: methodStatus.symbol,
                  interval: methodStatus.interval,
                  methodId: methodStatus.methodId,
                  updatedAt: new Date(),
                  isUpdated: true,
                });
              } else {
                await this.methodService.deleteMethodParam(
                  methodStatus.methodId,
                );
                await this.methodService.deleteMethodResult(
                  methodStatus.methodId,
                );
                await this.methodService.deleteMethodPerformance(
                  methodStatus.methodId,
                );
                await this.methodStatusService.deleteMethodStatus(
                  methodStatus.methodId,
                );
              }
            }
          }
        }
      }
      return;
    } catch (err) {
      this.logger.error(
        'Failed to update method data',
        logDetail({
          class: 'MethodUpdaterService',
          function: 'updateMethod',
          error: err,
        }),
      );
      return;
    }
  }

  async syncHelper(): Promise<void> {
    try {
      const methodIds = await this.methodService.getMethodIds();
      const methodIdsString = methodIds.map((item) => item.methodId);
      await this.methodService.deleteMethodResultNotIn(methodIdsString);
      await this.methodService.deleteMethodPerformanceNotIn(methodIdsString);
    } catch (err) {
      this.logger.error(
        'Failed to sync method data',
        logDetail({
          class: 'MethodUpdaterService',
          function: 'syncHelper',
          error: err,
        }),
      );
      return;
    }
  }
}
