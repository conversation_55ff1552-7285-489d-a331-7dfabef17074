import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { InsertResult, Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';
import { MethodParamEntity } from 'src/entity/method-param.entity';
import { MethodResultEntity } from 'src/entity/method-result.entity';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { GetMethodDto } from 'src/dto/get-method.dto';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';

@Injectable()
export class MethodService {
  constructor(
    @InjectRepository(MethodParamEntity)
    private readonly MethodParamsRepository: Repository<MethodParamEntity>,
    @InjectRepository(MethodResultEntity)
    private readonly MethodResultRepository: Repository<MethodResultEntity>,
    @InjectRepository(MethodPerformanceEntity)
    private readonly MethodPerformanceRepository: Repository<MethodPerformanceEntity>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async deleteMethodResultNotIn(methodIds: string[]): Promise<void> {
    try {
      await this.MethodResultRepository.manager.transaction(
        async (transactionalEntityManager) => {
          await transactionalEntityManager
            .createQueryBuilder()
            .delete()
            .from(MethodResultEntity)
            .where('methodId NOT IN (:...methodIds)', { methodIds })
            .execute();
        },
      );
    } catch (err) {
      this.logger.error(
        'Failed to delete method result data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodResultNotIn',
          error: err,
        }),
      );
      return;
    }
  }

  async deleteMethodPerformanceNotIn(methodIds: string[]): Promise<void> {
    try {
      await this.MethodPerformanceRepository.manager.transaction(
        async (transactionalEntityManager) => {
          await transactionalEntityManager
            .createQueryBuilder()
            .delete()
            .from(MethodPerformanceEntity)
            .where('methodId NOT IN (:...methodIds)', { methodIds })
            .execute();
        },
      );
    } catch (err) {
      this.logger.error(
        'Failed to delete method performance data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodPerformanceNotIn',
          error: err,
        }),
      );
      return;
    }
  }

  async getMethodIds(): Promise<MethodParamEntity[]> {
    try {
      const queryBuilder =
        this.MethodParamsRepository.createQueryBuilder('method-param');
      return await queryBuilder.select('method-param.methodId').getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-param data',
        logDetail({
          class: 'MethodService',
          function: 'getMethodIds',
          error: err,
        }),
      );
      return [];
    }
  }

  async insertMethodParam(param: GetBacktestMethodDto): Promise<InsertResult> {
    try {
      const result = await this.MethodParamsRepository.upsert(
        param,
        ['methodId'], // assuming methodId is the conflict target
      );

      return result;
    } catch (err) {
      this.logger.error(
        'Failed to insert method-param data',
        logDetail({
          class: 'MethodParamsService',
          function: 'insertMethodParam',
          error: err,
          param,
        }),
      );
      return { identifiers: [], generatedMaps: [], raw: [] } as InsertResult;
    }
  }

  async insertMethodResult(param: MethodResult[]): Promise<InsertResult> {
    try {
      const result = await this.MethodResultRepository.upsert(param, ['id']);
      return result;
    } catch (err) {
      this.logger.error(
        'Failed to insert method-result data',
        logDetail({
          class: 'AppService',
          function: 'insertMethodResult',
          error: err,
          param,
        }),
      );
      return { identifiers: [], generatedMaps: [], raw: [] } as InsertResult;
    }
  }
  async insertMethodPerformance(
    param: BacktestPerformance,
  ): Promise<InsertResult | UpdateResult> {
    try {
      // 1. Normalisasi data
      param.methodId = param.methodId.trim();

      // 2. Coba update terlebih dahulu
      const updateResult = await this.MethodPerformanceRepository.update(
        {
          methodId: param.methodId,
        },
        {
          fromDate: param.fromDate,
          endDate: param.endDate,
          totalValidTrade: param.totalValidTrade,
          totalInvalidTrade: param.totalInvalidTrade,
          probability: param.probability,
          averageRewardRiskRatio: param.averageRewardRiskRatio,
          maxOpenPosition: param.maxOpenPosition,
          maxConsecutiveLoss: param.maxConsecutiveLoss,
          maxConsecutiveProfit: param.maxConsecutiveProfit,
          cumulativePercentage: param.cumulativePercentage,
          maxHoldingPeriod: param.maxHoldingPeriod,
          avgValidTradeByMonth: param.avgValidTradeByMonth,
          avgValidTradeByDay: param.avgValidTradeByDay,
          avgRevenueByTrade: param.avgRevenueByTrade,
          avgOpenPosition: param.avgOpenPosition,
          avgStopPercent: param.avgStopPercent,
          avgProfitPercent: param.avgProfitPercent,
          avgConsecutiveLoss: param.avgConsecutiveLoss,
          avgConsecutiveProfit: param.avgConsecutiveProfit,
          avgHoldingPeriod: param.avgHoldingPeriod,
          maxStopPercent: param.maxStopPercent,
          maxProfitPercent: param.maxProfitPercent,
        },
      );

      // 3. Jika tidak ada yang diupdate, lakukan insert
      if (updateResult.affected === 0) {
        return await this.MethodPerformanceRepository.insert(param);
      }

      return updateResult;
    } catch (err) {
      // 4. Handle race condition
      if (err.code === '23505') {
        // Duplicate key
        return await this.MethodPerformanceRepository.update(
          {
            methodId: param.methodId,
            fromDate: param.fromDate,
            endDate: param.endDate,
          },
          param,
        );
      }
      this.logger.error(
        'Failed to insert method-performance data',
        logDetail({
          class: 'AppService',
          function: 'insertMethodPerformance',
          error: err,
          param,
        }),
      );
      throw err;
    }
  }

  async getParam(param: GetMethodDto): Promise<MethodParamEntity[]> {
    try {
      const queryBuilder =
        this.MethodParamsRepository.createQueryBuilder('method-param');

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-param.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-param data',
        logDetail({
          class: 'AppService',
          function: 'read',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  async getResult(param: GetMethodDto): Promise<MethodResultEntity[]> {
    try {
      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-result.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getResult',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  async getResultById(id: string): Promise<MethodResultEntity | null> {
    try {
      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      const result = await queryBuilder
        .where('method-result.id = :id', { id })
        .getOne();
      return result as MethodResultEntity;
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getResultById',
          error: err,
          param: id,
        }),
      );
      return null;
    }
  }

  async getPendingOrOpenResult(): Promise<MethodResultEntity[]> {
    try {
      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      queryBuilder.andWhere(
        'method-result.status = :status1 OR method-result.status = :status2',
        {
          status1: 'pending',
          status2: 'open',
        },
      );

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getPendingOpenResult',
          error: err,
        }),
      );
      return [];
    }
  }

  async getPerformance(
    param: GetMethodDto,
  ): Promise<MethodPerformanceEntity[]> {
    try {
      const queryBuilder =
        this.MethodPerformanceRepository.createQueryBuilder(
          'method-performance',
        );

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-performance.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-performance data',
        logDetail({
          class: 'AppService',
          function: 'getPerformance',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  private async getMethodsByPerformance(
    param: GetBacktestMultiMethodDto,
  ): Promise<MethodPerformanceEntity[]> {
    const { minProbability, methodLimit } = param;

    try {
      const rawResults = await this.MethodPerformanceRepository.query(
        `
          SELECT "methodId", "probability"
          FROM (
            SELECT   
              mp2."methodId", mp."probability",
              ROW_NUMBER() OVER (
                PARTITION BY mp2."symbol", mp2."interval", mp2."orderType", mp2."pattern"
                ORDER BY mp."probability" DESC
              ) AS rn
            FROM public."method-performance" mp  
            JOIN public."method-param" mp2   
              ON mp."methodId" = mp2."methodId"  
            WHERE mp."probability" >= $1
          ) AS sub
          WHERE rn = 1
          ORDER BY "probability" DESC
          LIMIT $2;
          `,
        [minProbability ?? 70, methodLimit ?? 1000],
      );

      return rawResults.map((item: any) => item.methodId);
    } catch (err) {
      this.logger.error(
        'Failed to read method-performance data',
        logDetail({
          class: 'AppService',
          function: 'getMethodsByPerformance',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  async getMultiMethodResult(param: GetBacktestMultiMethodDto) {
    try {
      const methodIds = await this.getMethodsByPerformance(param);

      if (methodIds.length === 0) {
        return [];
      }

      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      queryBuilder.where('method-result.methodId IN (:...methodIds)', {
        methodIds,
      });

      queryBuilder.orderBy('method-result.date', 'ASC');

      const result = await queryBuilder.getMany();
      return result.map((item: any) => ({
        ...item,
        date: new Date(item.date),
        openDate: item.openDate ? new Date(item.openDate) : null,
        closedDate: item.closedDate ? new Date(item.closedDate) : null,
        expiryDate: new Date(item.expiryDate),
      }));
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getResultsByMethods',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  async deleteMethodResult(methodId: string): Promise<void> {
    try {
      await this.MethodResultRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-result data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodResult',
          error: err,
        }),
      );
      return;
    }
  }

  async deleteMethodPerformance(methodId: string): Promise<void> {
    try {
      await this.MethodPerformanceRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-performance data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodPerformance',
          error: err,
        }),
      );
      return;
    }
  }

  async deleteMethodParam(methodId: string): Promise<void> {
    try {
      await this.MethodParamsRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-param data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodParam',
          error: err,
        }),
      );
      return;
    }
  }
}
