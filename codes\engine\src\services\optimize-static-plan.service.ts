import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { BacktestService } from './backtest.service';

import configurations from 'src/configurations';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { MethodResult } from 'src/interface/method-result.interface';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Pattern } from 'src/interface/pattern.interface';
import { Historical } from 'src/interface/historical.interface';
import { PatternService } from './pattern.service';
import { StaticPlanService } from './static-plan.service';
import { Plan } from 'src/interface/plan.interface';
import { OptimizedStaticPlan } from 'src/interface/optimized-static-plan.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { InstrumentService } from './instrument.service';
import { logDetail } from 'src/util/log-detail.util';
import { HistoricalCacheService } from './historical-cache.service';

@Injectable()
export class OptimizeStaticPlanService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly backtestService: BacktestService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly patternService: PatternService,
    private readonly staticPlanService: StaticPlanService,
    private readonly instrumentService: InstrumentService,
  ) {}

  async optimize(
    param: GetStaticPlanDto,
    historical?: Historical[],
    historicalExecution?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    plan?: Plan[],
    results?: MethodResult[],
  ): Promise<OptimizedStaticPlan> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));

      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          end: new Date(),
          interval: configurations('EXECUTION_INTERVAL'),
        }));

      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));

      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));

      const planData =
        plan ??
        (await this.staticPlanService.getPlan(
          param,
          historicalData,
          patternData,
          instrumentData,
        ));

      const resultsData =
        results ??
        (await this.backtestService.getSingleSymbolResult(
          planData,
          historicalExecutionData,
        ));

      const optimizedPlan: OptimizedStaticPlan = {
        entryPercentByClose: 0,
        rewardPercent: 0,
        riskPercent: 0,
      };

      if (!resultsData.length) return optimizedPlan;

      const profitResults = resultsData.filter(
        (item) => item.status === 'profit',
      );

      if (!profitResults.length) return optimizedPlan;

      const targetEntryList: number[] = [];
      const targetProfitList: number[] = [];
      const targetLossList: number[] = [];

      for (const item of profitResults) {
        const pattern = historicalData.find(
          (h) => h.date.getTime() === item.date.getTime(),
        );
        if (!pattern) continue;
        const historicalDataForExecution = historicalData.filter(
          (h) => h.date.getTime() > item.date.getTime(),
        );
        if (!historicalDataForExecution.length) continue;

        const startExecution = historicalDataForExecution[0];

        const historicalExecutionSample = historicalExecutionData.filter(
          (historicalItem) =>
            historicalItem.date.getTime() >= startExecution.date.getTime(),
        );

        if (!historicalExecutionSample.length) continue;

        let stopDate = new Date();

        const stopItem = historicalExecutionSample.filter((h) =>
          param.orderType === 'long'
            ? h.low <= item.stopLoss
            : h.high >= item.stopLoss,
        );

        if (stopItem.length) {
          stopDate = stopItem[0].date;
        }

        const historicalExecutionSampleForProfit =
          historicalExecutionSample.filter(
            (h) => h.date.getTime() < stopDate.getTime(),
          );
        if (historicalExecutionSampleForProfit.length === 0) continue;

        const profitItem =
          param.orderType === 'long'
            ? historicalExecutionSampleForProfit.reduce((prev, curr) =>
                prev.high > curr.high ? prev : curr,
              )
            : historicalExecutionSampleForProfit.reduce((prev, curr) =>
                prev.low < curr.low ? prev : curr,
              );

        const profitRatio =
          (param.orderType === 'long' ? profitItem.high : profitItem.low) /
          pattern.close;
        targetProfitList.push(profitRatio);

        const historicalExecutionSampleForStop =
          historicalExecutionSampleForProfit.filter(
            (h) => h.date.getTime() < profitItem.date.getTime(),
          );
        if (historicalExecutionSampleForStop.length === 0) continue;

        const lossItem =
          param.orderType === 'long'
            ? historicalExecutionSampleForStop.reduce((prev, curr) =>
                prev.low < curr.low ? prev : curr,
              )
            : historicalExecutionSampleForStop.reduce((prev, curr) =>
                prev.high > curr.high ? prev : curr,
              );

        const lossRatio =
          (param.orderType === 'long' ? lossItem.low : lossItem.high) /
          pattern.close;

        targetLossList.push(lossRatio);

        const historicalExecutionSampleForEntry =
          historicalExecutionSampleForProfit.filter(
            (h) => h.date.getTime() < item.expiryDate.getTime(),
          );

        if (!historicalExecutionSampleForEntry.length) continue;

        const entryItem =
          item.orderType === 'long'
            ? historicalExecutionSampleForEntry.reduce((prev, curr) =>
                prev.low < curr.low ? prev : curr,
              )
            : historicalExecutionSampleForEntry.reduce((prev, curr) =>
                prev.high > curr.high ? prev : curr,
              );

        const entryRatio =
          (item.orderType === 'long' ? entryItem.low : entryItem.high) /
          pattern.close;
        targetEntryList.push(entryRatio);
      }

      if (
        !targetProfitList.length ||
        !targetLossList.length ||
        !targetEntryList.length
      ) {
        return optimizedPlan;
      }

      const entryPercentByClose =
        100 *
        (param.orderType === 'long'
          ? Math.max(...targetEntryList) - 1
          : Math.min(...targetEntryList) - 1);

      const rewardPercent =
        100 *
        (param.orderType === 'long'
          ? Math.min(...targetProfitList) / Math.max(...targetEntryList) - 1
          : Math.max(...targetProfitList) / Math.min(...targetEntryList) - 1);

      const initialRiskPercent =
        100 *
        (param.orderType === 'long'
          ? Math.min(...targetLossList) / Math.max(...targetEntryList) - 1
          : Math.max(...targetLossList) / Math.min(...targetEntryList) - 1);

      const riskPercent =
        param.orderType === 'long' && initialRiskPercent > -0.01
          ? initialRiskPercent - 0.01
          : param.orderType === 'short' && initialRiskPercent < 0.01
            ? initialRiskPercent + 0.01
            : initialRiskPercent;
      return {
        entryPercentByClose,
        rewardPercent,
        riskPercent,
      };
    } catch (error) {
      this.logger.error(
        'Failed to fetch pattern data',
        logDetail({
          class: 'AppService',
          function: 'getPlan',
          param,
          error,
        }),
      );
      return {
        entryPercentByClose: 0,
        rewardPercent: 0,
        riskPercent: 0,
      };
    }
  }

  async getOptimizedStaticPlan(
    param: GetStaticPlanDto,
    historical?: Historical[],
    historicalExecution?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    plan?: Plan[],
    results?: MethodResult[],
  ): Promise<Plan[]> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));

      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          end: new Date(),
          interval: configurations('EXECUTION_INTERVAL'),
        }));

      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));

      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));

      const planData =
        plan ??
        (await this.staticPlanService.getPlan(
          param,
          historicalData,
          patternData,
          instrumentData,
        ));

      const resultsData =
        results ??
        (await this.backtestService.getSingleSymbolResult(
          planData,
          historicalExecutionData,
        ));

      const optimizedParam = await this.optimize(
        param,
        historicalData,
        historicalExecutionData,
        patternData,
        instrumentData,
        planData,
        resultsData,
      );
      const optimizedPlan = await this.staticPlanService.getPlan(
        { ...param, ...optimizedParam },
        historicalData,
        patternData,
        instrumentData,
      );
      return optimizedPlan;
    } catch (error) {
      this.logger.error(
        'Failed to fetch pattern data',
        logDetail({
          class: 'AppService',
          function: 'getPlan',
          param,
          error,
        }),
      );
      return [];
    }
  }
}
