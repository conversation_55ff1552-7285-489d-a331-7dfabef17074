import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { CandlestickPatternService } from './candlestick-pattern.service';
import { Historical } from 'src/interface/historical.interface';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';
import { GetPatternDto } from 'src/dto/get-pattern.dto';
import { Pattern } from 'src/interface/pattern.interface';

import { HistoricalCacheService } from './historical-cache.service';

@Injectable()
export class PatternService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly candlestickPatternService: CandlestickPatternService,
    private readonly historicalCacheService: HistoricalCacheService,
  ) {}

  async getPattern(
    param: GetPatternDto,
    historical?: Historical[],
  ): Promise<Pattern[]> {
    try {
      const historicalData =
        historical ??
        (await this.historicalCacheService.getHistorical({
          symbol: param.symbol,
          interval: param.interval,
          start: param.start,
          end: param.end,
          limit: **********,
          sort: 'ASC',
        }));

      const patternData: Pattern[] = [];
      for (
        let i = configurations('PATTERN_TREND_SAMPLE_LENGTH');
        i < historicalData.length;
        i++
      ) {
        const pattern = this.candlestickPatternService[param.pattern]({
          candles: historicalData.slice(
            i - configurations('PATTERN_TREND_SAMPLE_LENGTH'),
            i,
          ),
          trend: param.trend,
        });

        if (pattern) {
          patternData.push({
            ...historicalData[i - 1],
            patternType: param.patternType,
            pattern: param.pattern,
          });
        }
      }

      return patternData;
    } catch (error) {
      this.logger.error(
        'Failed to fetch pattern data',
        logDetail({
          class: 'PatternService',
          function: 'getPattern',
          param,
          error,
        }),
      );
      return [];
    }
  }
}
