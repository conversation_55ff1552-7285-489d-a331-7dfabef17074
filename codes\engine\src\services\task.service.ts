import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import configurations from 'src/configurations';
import { HistoricalIngestionService } from './historical-ingestion.service';
import { InstrumentIngestionService } from './instrument-ingestion.service';
import { MethodStatusService } from './method-status.service';
import { MethodUpdaterService } from './method-updater.service';
import { MethodIngestionService } from './method-ingestion.service';
import { InstrumentService } from './instrument.service';
import { shuffleArray } from 'src/util/suffle-array.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import { TraderExecutorService } from './trade-executor.service';
import { TradeResultUpdaterService } from './trade-result-updater.service';

@Injectable()
export class TaskService implements OnModuleInit {
  private readonly logger = new Logger(TaskService.name);
  private isEngineRunning = false;

  constructor(
    private readonly historicalIngestionService: HistoricalIngestionService,
    private readonly methodStatusService: MethodStatusService,
    private readonly instrumentIngestionService: InstrumentIngestionService,
    private readonly methodUpdaterService: MethodUpdaterService,
    private readonly methodIngestionService: MethodIngestionService,
    private readonly instrumentService: InstrumentService,
    private readonly traderExecutorService: TraderExecutorService,
    private readonly tradeResultUpdaterService: TradeResultUpdaterService,
  ) {}

  async onModuleInit() {
    const engineMode = configurations('ENGINE_MODE');
    const tradeExecutorEnabled = configurations('TRADE_EXECUTOR_ENABLED');
    if (engineMode === 'adapter') return;
    if (engineMode === 'worker') {
      await this.instrumentIngestionService.ingest(true);
      await this.historicalIngestionService.ingestHistorical();

      const disableCluster = configurations('DISABLE_CLUSTER');
      let symbols = await this.instrumentService.getSymbols();

      symbols =
        disableCluster === 'false'
          ? shuffleArray(getSymbolsSlice(symbols))
          : shuffleArray(symbols);

      this.logger.log('Method ingestion loop triggered.');
      // Jalankan ingestForever tanpa await agar tidak memblokir onModuleInit
      // tapi tangani error dengan proper logging
      this.methodIngestionService.ingestForever(symbols).catch((error) => {
        this.logger.error('Method ingestion forever failed:', error);
      });
    }
    if (engineMode === 'service') {
      this.isEngineRunning = true;
      const now = new Date();
      await this.instrumentIngestionService.ingest(true);
      await this.historicalIngestionService.ingestHistorical();
      await this.methodStatusService.resetMethodStatusByRunningResult();
      await this.methodUpdaterService.updateMethod(now);
      await this.tradeResultUpdaterService.updateTradeResult();
      if (tradeExecutorEnabled) await this.traderExecutorService.run();
      this.isEngineRunning = false;
    }
  }

  @Cron('0 */4 * * *')
  async handleInstrumentStatus() {
    const engineMode = configurations('ENGINE_MODE');
    if (engineMode === 'adapter') return;
    this.logger.log('Instrument ingestion job triggered.');
    await this.instrumentIngestionService.ingest();
  }

  @Cron('* * * * *')
  async handleInterval() {
    const now = new Date();
    const minutes = now.getMinutes();
    const hours = now.getHours();
    const dayOfMonth = now.getDate();
    const dayOfWeek = now.getDay();
    const executionInterval = configurations('EXECUTION_INTERVAL');
    const engineMode = configurations('ENGINE_MODE');
    const intervals = configurations('INTERVALS').filter(
      (item: string) => item !== executionInterval,
    );

    // if worker / adapter, skip
    if (engineMode === 'worker' || engineMode === 'adapter') return;

    // if service
    try {
      for (const interval of intervals) {
        switch (interval) {
          case 'M':
            if (minutes === 0 && hours === 0 && dayOfMonth === 1) {
              this.logger.log('Monthly job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            continue;
          case 'W':
            if (minutes === 0 && hours === 0 && dayOfWeek === 1) {
              this.logger.log('Weekly job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            continue;
          case 'D':
            if (minutes === 0 && hours === 0) {
              this.logger.log('Daily job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            continue;
          default:
            const intervalMinutes = parseInt(interval);
            if (isNaN(intervalMinutes)) {
              this.logger.warn(`Invalid interval: ${interval}`);
              continue;
            }

            if (
              intervalMinutes >= 60 &&
              minutes === 0 &&
              hours % (intervalMinutes / 60) === 0
            ) {
              this.logger.log(
                `Interval ${intervalMinutes / 60}h reset triggered.`,
              );
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
            }
            continue;
        }
      }

      if (this.isEngineRunning === false) {
        this.isEngineRunning = true;
        const tradeExecutorEnabled = configurations('TRADE_EXECUTOR_ENABLED');
        this.logger.log(`Interval ${executionInterval}m job triggered.`);
        await this.historicalIngestionService.ingestHistorical(
          executionInterval,
        );
        await this.methodStatusService.resetMethodStatusByRunningResult();
        await this.methodUpdaterService.updateMethod(now);
        await this.tradeResultUpdaterService.updateTradeResult();
        if (tradeExecutorEnabled) await this.traderExecutorService.run();
        this.isEngineRunning = false;
      }
    } catch (err) {
      this.logger.error(`Error during reset:`, err);
      this.isEngineRunning = false;
    }
  }
}
