import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { BacktestMultiMethodService } from './backtest-multi-method.service';
import configurations from 'src/configurations';
import { calculateEquityRatio } from 'src/util/calculate-equity-ratio.util';
import { InstrumentService } from './instrument.service';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';
import { MethodResult } from 'src/interface/method-result.interface';
import { OrderParamsV5 } from 'bybit-api';
import { AdapterService } from './adapter.service';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { getOrderTrigger } from 'src/util/get-order-trigger.util';
import { TradeResultService } from './trade-result.service';
import { TradeResult } from 'src/interface/trade-result.interface';
import { v4 as uuidv4 } from 'uuid';
import { delay } from 'src/util/delay.util';
import { MethodService } from './method.service';
import { generateOrderValue } from 'src/util/generate-order-value.util';
import { generateorderLinkId } from 'src/util/generate-order-link-id.util';
import { HistoricalCacheService } from './historical-cache.service';
import { shuffleArray } from 'src/util/suffle-array.util';

@Injectable()
export class TraderExecutorService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly backtestMultiMethodService: BacktestMultiMethodService,
    private readonly instrumentService: InstrumentService,
    private readonly adapterService: AdapterService,
    private readonly tradeResultService: TradeResultService,
    private readonly methodService: MethodService,
    private readonly historicalCacheService: HistoricalCacheService,
  ) {}

  async run() {
    try {
      const minProbability = configurations('TRADE_MIN_PROBABILITY');
      const methodLimit = configurations('METHOD_LIMIT');

      const backtest =
        await this.backtestMultiMethodService.getBacktestMutilMethodBoth({
          minProbability,
          methodLimit,
          pendingResultOnly: true,
        });
      const results = await this.validatePendingResults(backtest.result);
      const equityRatio = await this.equityRatio(backtest.performance);
      const orderPlans = await this.orderPlan({
        results,
        equityRatio,
      });
      await this.place(orderPlans);
      await this.cancel();
      return;
    } catch (error) {
      this.logger.error(
        'Failed to run',
        logDetail({
          class: 'TraderExecutorService',
          function: 'run',
          error,
        }),
      );
      await delay(5000);
      return;
    }
  }

  private async validatePendingResults(results: MethodResult[]) {
    const pendingResults: MethodResult[] = [];
    const now = Date.now();

    for (const result of results) {
      //CHECK HISTORY EXECUTION UPDATED
      const instrument = await this.instrumentService.getInstrument({
        symbol: result.symbol,
      });
      if (!instrument) continue;
      const rowTargetLength =
        (now - Number(instrument[0].listedTime)) /
        toMiliseconds(configurations('EXECUTION_INTERVAL'));

      const rowLength = await this.historicalCacheService.countHistorical(
        result.symbol,
        configurations('EXECUTION_INTERVAL'),
      );
      if (rowLength < rowTargetLength) continue;

      //CHECK ON TRADE RESULT IS CLOSED?

      const tradeResult = await this.tradeResultService.getClosedResults(100);
      const isClosed = tradeResult.some(
        (item) => item.methodId === result.methodId,
      );
      if (isClosed) continue;

      //PUSH TO PENDING RESULTS
      pendingResults.push(result);
    }

    return pendingResults;
  }

  private async cancel() {
    const cpuAllocation =
      configurations('CPU_ALLOCATION') ?? configurations('TOTAL_CORE');
    try {
      const symbols = await this.instrumentService.getSymbols();
      for (const symbol of shuffleArray(symbols)) {
        const activeOrders = await this.adapterService.getActiveOrders({
          category: 'linear',
          symbol,
        });
        if (!activeOrders) return;
        for (const activeOrder of activeOrders) {
          if (activeOrder.price === '0') continue;
          const tradeResults =
            await this.tradeResultService.getTradeResultByOrderId(
              activeOrder.orderId,
            );
          if (!tradeResults.length) {
            await this.adapterService.cancelOrder({
              category: 'linear',
              symbol: activeOrder.symbol,
              orderId: activeOrder.orderId,
            });
            continue;
          }
          for (const tradeResult of tradeResults) {
            if (
              tradeResult.status === 'pending' ||
              tradeResult.status === 'open'
            )
              continue;
            await this.adapterService.cancelOrder({
              category: 'linear',
              symbol: activeOrder.symbol,
              orderId: activeOrder.orderId,
            });

            await delay((1000 / 10) * cpuAllocation);
          }
        }
      }
      return;
    } catch (error) {
      this.logger.error({
        class: 'OrderPlacerService',
        object: 'orderCanceler',
        error: error.message,
      });
      return;
    }
  }

  private async place(orderPlans: TradeResult[]) {
    try {
      const cpuAllocation =
        configurations('CPU_ALLOCATION') ?? configurations('TOTAL_CORE');
      for (const orderPlan of orderPlans) {
        const lastPrice = await this.adapterService.fetchBybitHistorical({
          symbol: orderPlan.symbol,
          interval: 'D',
          limit: 1,
          start: new Date(Date.now() - toMiliseconds('D')),
          end: new Date(),
          sort: 'DESC',
        });
        const tickSize = await this.instrumentService.getTickSize(
          orderPlan.symbol,
        );

        const updatedOrderPlan: TradeResult = {
          ...orderPlan,
          ...getOrderTrigger({
            side: orderPlan.side,
            last: lastPrice[0].close.toString(),
            entry: orderPlan.price || '0',
            tickSize: tickSize,
          }),
        };
        const minOfMaxLeverage =
          await this.instrumentService.getMinOfMaxLeverage();

        const order: OrderParamsV5 = generateOrderValue(updatedOrderPlan);
        await this.adapterService.setLeverage({
          category: 'linear',
          symbol: orderPlan.symbol,
          buyLeverage: minOfMaxLeverage.toFixed(2),
          sellLeverage: minOfMaxLeverage.toFixed(2),
        });

        await this.adapterService.switchToHedgeMode();
        const activeOrders = await this.adapterService.getActiveOrders({
          category: 'linear',
          symbol: orderPlan.symbol,
        });

        if (activeOrders.length) {
          for (const order of activeOrders) {
            if (
              order.price === updatedOrderPlan.price &&
              order.takeProfit === updatedOrderPlan.takeProfit &&
              order.stopLoss === updatedOrderPlan.stopLoss &&
              order.side === updatedOrderPlan.side
            ) {
              updatedOrderPlan.orderId = order.orderId;
              await this.adapterService.amendOrder({
                ...updatedOrderPlan,
                orderId: order.orderId,
              });
              await this.tradeResultService.save(updatedOrderPlan);
            }
          }
        } else {
          const response = await this.adapterService.placeOrder(order);
          if (response && response.result.orderId) {
            updatedOrderPlan.orderId = response.result.orderId;
            await this.tradeResultService.save(updatedOrderPlan);
          }
        }
        await delay((1000 / 10) * cpuAllocation);
      }
    } catch (error) {
      this.logger.error(
        'Failed to place order',
        logDetail({
          class: 'TraderExecutorService',
          function: 'place',
          error,
        }),
      );
      await delay(5000);
      return await this.place(orderPlans);
    }
  }

  private async orderPlan(param: {
    results: MethodResult[];
    equityRatio: number;
  }): Promise<TradeResult[]> {
    try {
      const orderPlans: TradeResult[] = [];

      for (const result of param.results) {
        const methodParam = await this.methodService.getParam({
          methodId: result.methodId,
        });
        if (!methodParam.length) continue;
        const orderLinkId = generateorderLinkId(methodParam[0]);
        const side = result.orderType === 'long' ? 'Buy' : 'Sell';
        const positionIdx = result.symbol.includes('USDT')
          ? result.orderType === 'long'
            ? 1
            : 2
          : 0;
        const qty = await this.calculateQty({
          equityRatio: param.equityRatio,
          entryPrice: result.entry,
          symbol: result.symbol,
        });

        const plan: TradeResult = {
          id: result.id || uuidv4(),
          category: 'linear',
          symbol: result.symbol,
          isLeverage: 1,
          side,
          orderType: 'Limit',
          qty: qty.toString(),
          price: result.entry.toString(),
          triggerDirection: result.orderType === 'long' ? 1 : 2,
          triggerPrice: '',
          triggerBy: 'LastPrice',
          positionIdx,
          orderLinkId,
          takeProfit: result.takeProfit.toString(),
          stopLoss: result.stopLoss.toString(),
          tpTriggerBy: 'LastPrice',
          slTriggerBy: 'LastPrice',
          tpslMode: 'Partial',
          tpOrderType: 'Market',
          slOrderType: 'Market',
          methodId: result.methodId,
          date: result.date,
          interval: result.interval,
          stopPercent: result.stopPercent,
          profitPercent: result.profitPercent,
          expiryDate: result.expiryDate,
          status: result.status,
          openDate: result.openDate,
          closedDate: result.closedDate,
        };
        orderPlans.push(plan);
      }
      return orderPlans;
    } catch (error) {
      this.logger.error(
        'Failed to order plan',
        logDetail({
          class: 'TraderExecutorService',
          function: 'orderPlan',
          error,
        }),
      );
      return [];
    }
  }

  private async equityRatio(
    performance: BacktestMultiPerformance,
  ): Promise<number> {
    const minOfMaxLeverage = await this.instrumentService.getAvgOfMaxLeverage();
    const maxFundingRate = await this.instrumentService.getMaxFundingRate();
    const minFundingInterval =
      await this.instrumentService.getMinFundingInterval();

    return calculateEquityRatio({
      minOfMaxLeverage,
      maxOpenPosition: performance.maxOpenPosition,
      maxConsecutiveLoss: performance.maxConsecutiveLoss,
      maxStopPercent: performance.maxStopPercent,
      maxHoldingPeriod: performance.maxHoldingPeriod,
      minFundingInterval,
      maxFundingRate,
    });
  }

  private async calculateQty(param: {
    equityRatio: number;
    entryPrice: number;
    symbol: string;
  }) {
    try {
      const walletBalance = await this.adapterService.getWalletBalance();
      const qtyStep = await this.instrumentService.getQtyStep(param.symbol);
      const mulQtyStep = qtyStep.toString().split('.')[1]?.length || 0;
      const maxOrderQty = await this.instrumentService.getMaxOrderQty(
        param.symbol,
      );
      const minOrderQty = await this.instrumentService.getMinOrderQty(
        param.symbol,
      );
      const rawQty = (walletBalance * param.equityRatio) / param.entryPrice;
      const stepCount = Math.floor(rawQty / qtyStep);
      const qty = stepCount * qtyStep;
      const finalQty = Math.max(
        Math.min(qty, maxOrderQty),
        minOrderQty,
      ).toFixed(mulQtyStep);
      return parseFloat(finalQty);
    } catch (error) {
      this.logger.error(
        'Failed to calculate qty',
        logDetail({
          class: 'TraderExecutorService',
          function: 'calculateQty',
          error,
        }),
      );
      await delay(5000);
      return await this.calculateQty(param);
    }
  }
}
