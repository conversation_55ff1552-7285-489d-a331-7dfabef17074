import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { TradeResultService } from './trade-result.service';
import { MethodService } from './method.service';
import { AdapterService } from './adapter.service';

@Injectable()
export class TradeResultUpdaterService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly tradeResultService: TradeResultService,
    private readonly methodService: MethodService,
  ) {}

  async updateTradeResult() {
    try {
      const activeResults = await this.tradeResultService.getActiveResults();
      for (const result of activeResults) {
        const methodResult = await this.methodService.getResultById(result.id);
        if (!methodResult) {
          if (result.status === 'pending') {
            await this.tradeResultService.updateStatus({
              ...result,
              status: 'canceled',
            });
          } else {
            await this.tradeResultService.updateStatus({
              ...result,
              status: 'invalid',
            });
          }
          continue;
        }

        await this.tradeResultService.updateStatus({
          ...result,
          openDate: methodResult.openDate,
          closedDate: methodResult.closedDate,
          status: methodResult.status,
        });
      }
    } catch (error) {
      this.logger.error(
        'Failed to run update',
        logDetail({
          class: 'TradeResultUpdaterService',
          function: 'runUpdate',
          error,
        }),
      );
      return;
    }
  }
}
