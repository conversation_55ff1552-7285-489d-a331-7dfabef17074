import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { TradeResult } from 'src/interface/trade-result.interface';
import { TradeResultEntity } from 'src/entity/trade-result.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class TradeResultService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectRepository(TradeResultEntity)
    private readonly tradeResultRepository: Repository<TradeResultEntity>,
  ) {}

  async getByorderLinkId(orderLinkId: string): Promise<TradeResultEntity[]> {
    try {
      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      return await queryBuilder
        .where('trade-result.orderLinkId = :orderLinkId', { orderLinkId })
        .getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getByorderLinkId',
          error: err,
        }),
      );
      return [];
    }
  }

  async save(param: TradeResult): Promise<void> {
    try {
      // Ganti 'orderId' dengan nama kolom unik yang benar di entitas TradeResult
      await this.tradeResultRepository.upsert(param, ['orderLinkId']);
    } catch (error: any) {
      this.logger.error(
        'Failed to upsert',
        logDetail({
          class: 'TradeResultService',
          function: 'save',
          error,
        }),
      );
      return;
    }
  }

  async getPendingResults(): Promise<TradeResult[]> {
    try {
      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      queryBuilder.andWhere('trade-result.status = :status', {
        status: 'pending',
      });
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getPendingResults',
          error: err,
        }),
      );
      return [];
    }
  }

  async getTradeResultByOrderId(orderId: string): Promise<TradeResultEntity[]> {
    try {
      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      return await queryBuilder
        .where('trade-result.orderId = :orderId', { orderId })
        .getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getTradeResultByOrderId',
          error: err,
        }),
      );
      return [];
    }
  }

  async getTradeResultByPrice(param: {
    price: string;
    side: string;
    stopLoss: string;
    takeProfit: string;
    symbol: string;
  }): Promise<TradeResultEntity[]> {
    try {
      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      queryBuilder.andWhere('trade-result.price = :price', {
        price: param.price,
      });
      queryBuilder.andWhere('trade-result.side = :side', { side: param.side });
      queryBuilder.andWhere('trade-result.stopLoss = :stopLoss', {
        stopLoss: param.stopLoss,
      });
      queryBuilder.andWhere('trade-result.takeProfit = :takeProfit', {
        takeProfit: param.takeProfit,
      });
      queryBuilder.andWhere('trade-result.symbol = :symbol', {
        symbol: param.symbol,
      });
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getTradeResultByPrice',
          error: err,
        }),
      );
      return [];
    }
  }

  async getActiveResults(): Promise<TradeResult[]> {
    try {
      // Check if orderId column exists first
      const columnExists = await this.checkOrderIdColumnExists();

      if (!columnExists) {
        this.logger.warn(
          'orderId column does not exist in trade-result table, using fallback query',
          logDetail({
            class: 'TradeResultService',
            function: 'getActiveResults',
          }),
        );

        // Use simple query without orderId column
        return await this.tradeResultRepository.find({
          where: [{ status: 'open' }, { status: 'pending' }],
        });
      }

      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      queryBuilder.andWhere(
        'trade-result.status = :status1 OR trade-result.status = :status2',
        {
          status1: 'open',
          status2: 'pending',
        },
      );
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getActiveResults',
          error: err,
        }),
      );
      return [];
    }
  }

  private async checkOrderIdColumnExists(): Promise<boolean> {
    try {
      const queryRunner =
        this.tradeResultRepository.manager.connection.createQueryRunner();
      const result = await queryRunner.query(`
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'trade-result'
        AND column_name = 'orderId'
      `);
      await queryRunner.release();
      return result.length > 0;
    } catch (error) {
      this.logger.warn(
        'Failed to check orderId column existence',
        logDetail({
          class: 'TradeResultService',
          function: 'checkOrderIdColumnExists',
          error,
        }),
      );
      return false;
    }
  }

  async getClosedResults(limit: number): Promise<TradeResult[]> {
    try {
      const queryBuilder =
        this.tradeResultRepository.createQueryBuilder('trade-result');

      queryBuilder.andWhere(
        'trade-result.status != :status1 AND trade-result.status != :status2',
        {
          status1: 'pending',
          status2: 'open',
        },
      );

      queryBuilder.orderBy('trade-result.updatedAt', 'DESC');
      queryBuilder.limit(limit);
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read trade-result data',
        logDetail({
          class: 'TradeResultService',
          function: 'getClosedResults',
          error: err,
        }),
      );
      return [];
    }
  }

  async updateStatus(param: TradeResult): Promise<void> {
    try {
      await this.tradeResultRepository.update(
        { orderLinkId: param.orderLinkId },
        { ...param, updatedAt: new Date() },
      );
    } catch (error) {
      this.logger.error(
        'Failed to update',
        logDetail({
          class: 'TradeResultService',
          function: 'updateStatus',
          error,
        }),
      );
      return;
    }
    return;
  }
}
