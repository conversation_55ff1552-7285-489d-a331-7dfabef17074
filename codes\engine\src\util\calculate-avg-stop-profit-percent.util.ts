import { MethodResult } from 'src/interface/method-result.interface';

export function calculateAvgStopProfitPercent(
  param: MethodResult[],
  target: 'stop' | 'profit',
) {
  const total = param.reduce((acc, cur) => {
    if (target === 'stop' && cur.status === 'loss') {
      acc += Math.abs(cur.stopPercent);
    }
    if (target === 'profit' && cur.status === 'profit') {
      acc += Math.abs(cur.profitPercent);
    }
    return acc;
  }, 0);
  const avg = total / param.length;
  return Number(avg.toFixed(2));
}
