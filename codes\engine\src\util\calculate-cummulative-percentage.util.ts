import { MethodResult } from 'src/interface/method-result.interface';

export function calculateCummulativePercentage(param: MethodResult[]) {
  const cumulativePercentage = param.reduce((acc, cur) => {
    if (cur.status === 'profit') {
      acc += Math.abs(cur.profitPercent);
    }
    if (cur.status === 'loss') {
      acc -= Math.abs(cur.stopPercent);
    }
    return acc;
  }, 0);
  return Number(cumulativePercentage.toFixed(2));
}
