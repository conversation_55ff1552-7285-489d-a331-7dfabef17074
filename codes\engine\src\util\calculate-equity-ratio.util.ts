import configurations from 'src/configurations';

export function calculateEquityRatio(param: {
  minOfMaxLeverage: number;
  maxOpenPosition: number;
  maxConsecutiveLoss: number;
  maxStopPercent: number;
  maxHoldingPeriod: number;
  minFundingInterval: number;
  maxFundingRate: number;
}) {
  const estAvgDrawdown = configurations('ESTIMATED_MAX_DRAWDOWN');
  const optimumEquityAllocation = configurations('OPTIMUM_EQUITY_ALLOCATION');
  const ratioBasedOnMaxLeverage =
    param.minOfMaxLeverage / param.maxOpenPosition;
  const ratioBasedOnEstDrawdown =
    estAvgDrawdown / param.maxConsecutiveLoss / param.maxStopPercent;
  const ratioBasedOnMaxFundingRate =
    1 -
    (param.maxHoldingPeriod *
      (1440 / param.minFundingInterval) *
      param.maxFundingRate *
      param.maxOpenPosition) /
      100;

  return Math.min(
    Math.abs(ratioBasedOnMaxLeverage),
    Math.abs(ratioBasedOnEstDrawdown),
    Math.abs(ratioBasedOnMaxFundingRate),
    Math.abs(optimumEquityAllocation),
  );
}
