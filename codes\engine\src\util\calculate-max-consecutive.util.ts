import { MethodResult } from 'src/interface/method-result.interface';

export function calculateMaxConsecutive(
  param: MethodResult[],
  target: 'loss' | 'profit',
) {
  let consecutive = 0;
  let maxConsecutive = 0;

  for (const result of param.sort((a, b) => {
    const closedDateA = a.closedDate || a.date;
    const closedDateB = b.closedDate || b.date;
    return closedDateA.getTime() - closedDateB.getTime();
  })) {
    if (result.status === target) {
      consecutive++;
      maxConsecutive = Math.max(maxConsecutive, consecutive);
    } else {
      consecutive = 0;
    }
  }

  return maxConsecutive;
}
