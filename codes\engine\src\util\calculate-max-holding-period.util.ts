import { MethodResult } from 'src/interface/method-result.interface';

export function calculateMaxHoldingPeriod(param: MethodResult[]): number {
  let maxHoldingDays = 0;

  for (const record of param) {
    const { openDate, closedDate, status } = record;

    // <PERSON><PERSON> proses jika status profit/loss dan tanggal valid
    if ((status === 'profit' || status === 'loss') && openDate && closedDate) {
      const open = new Date(openDate);
      const close = new Date(closedDate);
      const holdingDays =
        (close.getTime() - open.getTime()) / (1000 * 60 * 60 * 24);

      if (holdingDays > maxHoldingDays) {
        maxHoldingDays = holdingDays;
      }
    }
  }

  return Math.ceil(maxHoldingDays);
}
