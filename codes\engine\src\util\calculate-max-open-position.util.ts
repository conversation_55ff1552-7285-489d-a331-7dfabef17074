import { MethodResult } from 'src/interface/method-result.interface';

export function calculateMaxOpenPosition(param: MethodResult[]): number {
  const sampleCountMap = new Map();
  const fiveMinutes = 1000 * 60 * 5;
  for (const result of param) {
    if (!result.closedDate) continue;
    const start = result.date.getTime();
    const end = (result.closedDate || new Date()).getTime();
    if (end < start) continue;
    const duration = end - start;
    const intervalCount = Math.floor(duration / fiveMinutes);
    for (let i = 0; i <= intervalCount; i++) {
      const timestamp = start + i * fiveMinutes;
      const currentCount = sampleCountMap.get(timestamp) || 0;
      sampleCountMap.set(timestamp, currentCount + 1);
    }
  }

  let max = 0;
  for (const count of sampleCountMap.values()) {
    if (count > max) max = count;
  }
  return max;
}
