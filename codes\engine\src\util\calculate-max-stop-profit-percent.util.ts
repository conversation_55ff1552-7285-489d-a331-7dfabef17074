import { MethodResult } from 'src/interface/method-result.interface';

export function calculateMaxStopProfitPercent(
  param: MethodResult[],
  target: 'stop' | 'profit',
) {
  const maxStopProfitPercent = param.reduce((acc, cur) => {
    if (target === 'stop' && cur.status === 'loss') {
      acc = Math.min(acc, -Math.abs(cur.stopPercent));
    }
    if (target === 'profit' && cur.status === 'profit') {
      acc = Math.max(acc, Math.abs(cur.profitPercent));
    }
    return acc;
  }, 0);
  return maxStopProfitPercent
}
