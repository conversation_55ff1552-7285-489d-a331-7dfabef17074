import { MethodResult } from 'src/interface/method-result.interface';

export function calculateRewardRiskRatio(param: MethodResult[]) {
    const validTrade = param.filter(
        (item) => item.status === 'profit' || item.status === 'loss',
      )
      const totalRatio = validTrade.reduce((acc, cur) => {
        acc += Math.abs(cur.profitPercent) / Math.abs(cur.stopPercent);
        return acc;
      }, 0);
  return Number((totalRatio / validTrade.length).toFixed(2));
}
