import { OrderParamsV5 } from 'bybit-api';
import { TradeResult } from 'src/interface/trade-result.interface';

export function generateOrderValue(param: TradeResult): OrderParamsV5 {
  return {
    category: param.category,
    symbol: param.symbol,
    isLeverage: param.isLeverage,
    side: param.side,
    orderType: param.orderType,
    qty: param.qty,
    price: param.price,
    triggerDirection: param.triggerDirection,
    triggerPrice: param.triggerPrice,
    triggerBy: param.triggerBy,
    positionIdx: param.positionIdx,
    orderLinkId: param.id,
    takeProfit: param.takeProfit,
    stopLoss: param.stopLoss,
    tpTriggerBy: param.tpTriggerBy,
    slTriggerBy: param.slTriggerBy,
    tpslMode: param.tpslMode,
    tpOrderType: param.tpOrderType,
    slOrderType: param.slOrderType,
  };
}
