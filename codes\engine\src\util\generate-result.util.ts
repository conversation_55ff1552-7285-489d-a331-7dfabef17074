import { MethodResult } from 'src/interface/method-result.interface';
import { Plan } from 'src/interface/plan.interface';
import { toMiliseconds } from './to-milliseconds.util';
import { isMatchTargetPrice } from './is-match-target-price.util';
import { Historical } from 'src/interface/historical.interface';
import { isInvalidOpenPrice } from './is-invalid-open-price.util';

export function generateResult(
  param: Plan,
  historicalExecution: Historical[],
): MethodResult {
  const startDate = new Date(
    param.date.getTime() + toMiliseconds(param.interval),
  );
  const historicalExecutionSample = historicalExecution.filter(
    (historicalItem) => historicalItem.date.getTime() >= startDate.getTime(),
  );

  const result: MethodResult = {
    id: param.id, // ✅ Transfer ID dari Plan ke MethodResult
    methodId: param.methodId ?? '',
    orderType: param.orderType,
    date: param.date,
    symbol: param.symbol,
    entry: param.entry,
    stopLoss: param.stopLoss,
    takeProfit: param.takeProfit,
    stopPercent: param.stopPercent,
    profitPercent: param.profitPercent,
    expiryDate: param.expiryDate,
    interval: param.interval,
    status: 'pending',
  };

  for (const historical of historicalExecutionSample) {
    if (
      result.status === 'pending' &&
      historical.date.getTime() >= param.expiryDate.getTime()
    ) {
      result.status = 'expired';
      break;
    }

    if (
      result.status === 'pending' &&
      isMatchTargetPrice(param.entry, historical)
    ) {
      result.status = 'open';
      result.openDate = historical.date;
    }

    if (
      result.status === 'open' &&
      isMatchTargetPrice(param.stopLoss, historical) &&
      isMatchTargetPrice(param.takeProfit, historical)
    ) {
      result.status = 'invalid';
      result.closedDate = historical.date;
      break;
    }

    if (
      result.status === 'open' &&
      result.openDate === historical.date &&
      isMatchTargetPrice(param.stopLoss, historical) &&
      isMatchTargetPrice(param.entry, historical) &&
      isInvalidOpenPrice({
        entryPrice: param.entry,
        targetPrice: param.stopLoss,
        historical,
      })
    ) {
      result.status = 'invalid';
      result.closedDate = historical.date;
      break;
    }

    if (
      result.status === 'open' &&
      result.openDate === historical.date &&
      isMatchTargetPrice(param.takeProfit, historical) &&
      isMatchTargetPrice(param.entry, historical) &&
      isInvalidOpenPrice({
        entryPrice: param.entry,
        targetPrice: param.takeProfit,
        historical,
      })
    ) {
      result.status = 'invalid';
      result.closedDate = historical.date;
      break;
    }

    if (
      result.status === 'open' &&
      isMatchTargetPrice(param.stopLoss, historical)
    ) {
      result.status = 'loss';
      result.closedDate = historical.date;
      break;
    }

    if (
      result.status === 'open' &&
      isMatchTargetPrice(param.takeProfit, historical)
    ) {
      result.status = 'profit';
      result.closedDate = historical.date;
      break;
    }
  }
  return result;
}
