import { roundToTickSize } from "./round-to-tick-size.util";

export function getEntry(param: {
    orderType: string;
    closePrice: number;
    entryPercentByClose: number;
    tickSize: number;
  }): number {
    const price = param.closePrice * (1 + param.entryPercentByClose / 100);
    return roundToTickSize({
      mathRound: param.orderType === 'long' ? 'ceil' : 'floor',
      price,
      tickSize: param.tickSize,
    });
  }