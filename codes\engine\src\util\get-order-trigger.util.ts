import { roundToTickSize } from './round-to-tick-size.util';

export function getOrderTrigger(param: {
  side: 'Buy' | 'Sell';
  last: string;
  entry: string;
  tickSize: number;
}): {
  triggerDirection: 1 | 2;
  triggerPrice: string;
} {
  const tick = param.tickSize;
  const entry = Number(param.entry);
  const last = Number(param.last);
  const isLastHigher = last > entry;
  const isLastLower = last < entry;
  const triggerDirection: 1 | 2 = isLastLower
    ? 1
    : isLastHigher
      ? 2
      : param.side === 'Buy'
        ? 1
        : 2;
  const triggerPrice =
    isLastHigher || isLastLower
      ? entry
      : param.side === 'Buy'
        ? entry + tick
        : entry - tick;
  const output = {
    triggerDirection,
    triggerPrice: roundToTickSize({
      mathRound: 'floor',
      price: triggerPrice,
      tickSize: tick,
    }).toString(),
  };

  return output;
}
