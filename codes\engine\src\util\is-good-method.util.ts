import configurations from 'src/configurations';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { MethodResult } from 'src/interface/method-result.interface';

export function isGoodMethod(
  result: MethodResult[],
  performance: MethodPerformanceEntity,
): boolean {
  const minProbability = configurations('METHOD_MIN_PROBABILITY');
  const maxConsecutiveLoss = configurations('METHOD_MAX_CONSECUTIVE_LOSS');
  const minValidTrade = configurations('METHOD_MIN_VALID_TRADE');
  const resultLength = result.length > 0;

  return (
    resultLength &&
    performance.probability >= minProbability &&
    performance.maxConsecutiveLoss <= maxConsecutiveLoss &&
    !performance.totalInvalidTrade &&
    performance.totalValidTrade >= minValidTrade &&
    performance.averageRewardRiskRatio >= 1
  );
}
