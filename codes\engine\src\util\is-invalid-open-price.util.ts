import { Historical } from 'src/interface/historical.interface';

export function isInvalidOpenPrice(param: {
  entryPrice: number;
  targetPrice: number;
  historical: Historical;
}): boolean {
  const isInvalidTop =
    param.historical.open > param.entryPrice &&
    param.historical.open < param.targetPrice;
  const isInvalidBottom =
    param.historical.open < param.entryPrice &&
    param.historical.open > param.targetPrice;
  return isInvalidTop || isInvalidBottom;
}
