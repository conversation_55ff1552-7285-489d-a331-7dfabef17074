export function logDetail(param: {
  class?: string;
  function?: string;
  param?: any;
  query?: any;
  body?: any;
  header?: any;
  url?: string;
  status?: number;
  error?: any;
  results?: any;
}) {
  let output: any = param;
  if (param.error) {
    output = {
      ...param,
      code: param.error.code ?? 'UNKNOWN',
      message: param.error.message ?? 'No message',
      stack: param.error.stack ?? 'No stack trace',
      name: param.error.name ?? 'Error',
    };
    delete output.error;
  }
  return output;
}
