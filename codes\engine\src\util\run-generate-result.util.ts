import { MethodResult } from 'src/interface/method-result.interface';
import { Plan } from 'src/interface/plan.interface';
import { Historical } from 'src/interface/historical.interface';
import { generateResult } from './generate-result.util';

export function runGenerateResult(
  param: Plan[],
  historical: Historical[],
): MethodResult[] {
  const results: MethodResult[] = [];
  for (const item of param) {
    const result = generateResult(item, historical);
    results.push(result);
  }
  return results;
}
