apiVersion: apps/v1
kind: Deployment
metadata:
  name: krakend
  namespace: cfi-egl2
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: krakend
    spec:
      volumes:
        - name: krakend-pvc
          persistentVolumeClaim:
            claimName: krakend-pvc
        - name: krakend-configmap
          configMap:
            name: krakend-configmap
            defaultMode: 420
      initContainers:
        - name: krakend-init
          image: busybox
          command:
            - /bin/sh
            - '-c'
            - |
              if [ ! -f /etc/krakend/krakend.json ]; then
                echo "Copying krakend.json..."
                cp -r /tmp/initiation.json /etc/krakend/krakend.json
              fi
          resources:
            limits:
              cpu: 250m
              memory: 128Mi
            requests:
              cpu: 250m
              memory: 128Mi
          volumeMounts:
            - name: krakend-configmap
              readOnly: true
              mountPath: /tmp/initiation.json
              subPath: init-krakend.json
            - name: krakend-pvc
              mountPath: /etc/krakend
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      containers:
        - name: krakend
          image: >-
            807879955963.dkr.ecr.ap-southeast-3.amazonaws.com/clipan/egl2/krakend:cloud
          ports:
            - containerPort: 8080
              protocol: TCP
            - containerPort: 5000
              protocol: TCP
            - containerPort: 9092
              protocol: TCP
          envFrom:
            - configMapRef:
                name: krakend-be-env
          resources:
            limits:
              cpu: 70m
              memory: 2001Mi
            requests:
              cpu: 70m
              memory: 2001Mi
          volumeMounts:
            - name: krakend-pvc
              mountPath: /etc/krakend
            - name: krakend-configmap
              readOnly: true
              mountPath: /etc/krakend/AdditionalSetting-Krakend.json
              subPath: AdditionalSetting-Krakend.json
            - name: krakend-configmap
              readOnly: true
              mountPath: /etc/krakend/jwk.json
              subPath: jwk.json
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      imagePullSecrets:
        - name: ecr-secret-public
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
