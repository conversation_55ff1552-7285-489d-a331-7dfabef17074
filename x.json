{"endpoints": [{"endpoint": "/{sub_path}/{version}/Authenticate/Login", "backend": [{"url_pattern": "/{sub_path}/{version}/Authenticate/Login", "host": ["http://istio-ingress-internal.istio-ingress.svc.cluster.local:30001"], "encoding": "no-op", "method": "POST", "extra_config": {"backend/http": {"return_error_code": true}}, "headers_to_pass": ["*"], "disable_host_sanitize": false}], "output_encoding": "no-op", "method": "POST", "input_headers": ["*"], "extra_config": {"qos/ratelimit/router": {"max_rate": 15, "client_max_rate": 15, "strategy": "ip", "every": "30s", "capacity": 15}}}, {"endpoint": "/{sub_path}/{version}/{level1}", "backend": [{"url_pattern": "/{sub_path}/{version}/{level1}", "host": ["http://istio-ingress-internal.istio-ingress.svc.cluster.local:30001"], "encoding": "no-op", "method": "POST", "extra_config": {"backend/http": {"return_error_code": true}}, "headers_to_pass": ["*"], "disable_host_sanitize": false}], "output_encoding": "no-op", "method": "POST", "input_headers": ["*"], "extra_config": {}}, {"endpoint": "/{sub_path}/{version}/{level1}/{level2}", "backend": [{"url_pattern": "/{sub_path}/{version}/{level1}/{level2}", "host": ["http://istio-ingress-internal.istio-ingress.svc.cluster.local:30001"], "encoding": "no-op", "method": "POST", "extra_config": {"backend/http": {"return_error_code": true}}, "headers_to_pass": ["*"], "disable_host_sanitize": false}], "output_encoding": "no-op", "method": "POST", "input_headers": ["*"], "extra_config": {}}, {"endpoint": "/{sub_path}/{version}/{level1}/{level2}/{level3}", "backend": [{"url_pattern": "/{sub_path}/{version}/{level1}/{level2}/{level3}", "host": ["http://istio-ingress-internal.istio-ingress.svc.cluster.local:30001"], "encoding": "no-op", "method": "POST", "extra_config": {"backend/http": {"return_error_code": true}}, "headers_to_pass": ["*"], "disable_host_sanitize": false}], "output_encoding": "no-op", "method": "POST", "input_headers": ["*"], "extra_config": {}}, {"endpoint": "/{sub_path}/{version}/{level1}/{level2}/{level3}/{level4}", "backend": [{"url_pattern": "/{sub_path}/{version}/{level1}/{level2}/{level3}/{level4}", "host": ["http://istio-ingress-internal.istio-ingress.svc.cluster.local:30001"], "encoding": "no-op", "method": "POST", "extra_config": {"backend/http": {"return_error_code": true}}, "headers_to_pass": ["*"], "disable_host_sanitize": false}], "output_encoding": "no-op", "method": "POST", "input_headers": ["*"], "extra_config": {}}], "extra_config": {"qos/ratelimit/router": {"max_rate": 15, "client_max_rate": 15, "strategy": "ip", "every": "30s"}}}